<?php
#region region DOCS

/** @var Proyecto $proyecto */
/** @var int $proyectoId */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Proyecto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Gestión de Proyecto</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de proyecto" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>

<body>
<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Proyecto</h4>
				<p class="mb-0 text-muted">
					<?php if ($proyecto): ?>
						<?php echo htmlspecialchars($proyecto->getDescripcion()); ?>
					<?php else: ?>
						Administra los parámetros del proyecto
					<?php endif; ?>
				</p>
			</div>
			<div class="ms-auto">
				<a href="lproyectos" class="btn btn-secondary">
					<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Proyectos
				</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php if ($proyecto): ?>
		<?php #region region PROJECT MANAGEMENT CONTENT ?>
		<div class="row">
			<!-- Left Sidebar with Vertical Tabs -->
			<div class="col-md-3">
				<div class="panel panel-inverse no-border-radious">
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">Navegación</h4>
					</div>
					<div class="panel-body p-0">
						<div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
							<button class="nav-link active text-start" id="v-pills-informacion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-informacion" type="button" role="tab"
									aria-controls="v-pills-informacion" aria-selected="true">
								<i class="fa fa-info-circle fa-fw me-2"></i> Información
							</button>
							<button class="nav-link text-start" id="v-pills-inicio-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-inicio" type="button" role="tab"
									aria-controls="v-pills-inicio" aria-selected="false">
								<i class="fa fa-home fa-fw me-2"></i> Inicio
							</button>
							<button class="nav-link text-start" id="v-pills-planificacion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-planificacion" type="button" role="tab"
									aria-controls="v-pills-planificacion" aria-selected="false">
								<i class="fa fa-calendar-alt fa-fw me-2"></i> Planificación
							</button>
							<button class="nav-link text-start" id="v-pills-ejecucion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-ejecucion" type="button" role="tab"
									aria-controls="v-pills-ejecucion" aria-selected="false">
								<i class="fa fa-play-circle fa-fw me-2"></i> Ejecución
							</button>
							<button class="nav-link text-start" id="v-pills-monitoreo-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-monitoreo" type="button" role="tab"
									aria-controls="v-pills-monitoreo" aria-selected="false">
								<i class="fa fa-chart-line fa-fw me-2"></i> Monitoreo y Control
							</button>
							<button class="nav-link text-start" id="v-pills-cierre-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-cierre" type="button" role="tab"
									aria-controls="v-pills-cierre" aria-selected="false">
								<i class="fa fa-flag-checkered fa-fw me-2"></i> Cierre
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Main Content Area -->
			<div class="col-md-9">
				<div class="tab-content" id="v-pills-tabContent">

					<?php #region region TAB: INFORMACIÓN ?>
					<div class="tab-pane fade show active" id="v-pills-informacion" role="tabpanel"
						 aria-labelledby="v-pills-informacion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-info-circle fa-fw me-2"></i> Información del Proyecto
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">ID del Proyecto:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getId()); ?></p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Descripción:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getDescripcion()); ?></p>
										</div>
									</div>
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Creación:</label>
											<p class="form-control-plaintext">
												<?php
												$fechaCreacion = $proyecto->getFechaCreacion();
												if ($fechaCreacion) {
													// Format date to show only yyyy-MM-dd portion
													$fecha = date('Y-m-d', strtotime($fechaCreacion));
													echo htmlspecialchars($fecha);
												} else {
													echo 'N/A';
												}
												?>
											</p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Inicio:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getFechaInicio() ?? 'N/A'); ?></p>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="mb-3">
											<label class="form-label fw-bold">Estado:</label>
											<p class="form-control-plaintext">
												<?php if ($proyecto->isActivo()): ?>
													<span class="badge bg-success">Activo</span>
												<?php else: ?>
													<span class="badge bg-danger">Inactivo</span>
												<?php endif; ?>
											</p>
										</div>
									</div>
								</div>
								
								<div class="mt-4">
									<h5><i class="fa fa-cogs fa-fw me-2"></i> Acciones Rápidas</h5>
									<div class="d-flex gap-2 flex-wrap">
										<a href="eproyecto?id=<?php echo $proyecto->getId(); ?>" class="btn btn-primary btn-sm">
											<i class="fa fa-edit fa-fw me-1"></i> Editar Proyecto
										</a>
										<a href="lproyectos?action=ver_tareas&proyecto_id=<?php echo $proyecto->getId(); ?>" class="btn btn-info btn-sm">
											<i class="fa fa-tasks fa-fw me-1"></i> Ver Tareas
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: INFORMACIÓN ?>

					<?php #region region TAB: INICIO ?>
					<div class="tab-pane fade" id="v-pills-inicio" role="tabpanel"
						 aria-labelledby="v-pills-inicio-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-home fa-fw me-2"></i> Documentos de Inicio
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-12">
										<p class="text-muted mb-4">
											<i class="fa fa-info-circle me-1"></i>
											Sube los documentos iniciales del proyecto. Los archivos son opcionales y pueden ser reemplazados en cualquier momento.
										</p>
									</div>
								</div>

								<div class="row">
									<!-- Levantamiento de Necesidades -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-primary text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-clipboard-list me-2"></i>
													Levantamiento de Necesidades
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocLevantamientoNecesidades()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocLevantamientoNecesidades()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocLevantamientoNecesidades()); ?>"
														   class="btn btn-sm btn-outline-primary" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_levantamiento_necesidades">

													<div class="mb-3">
     									<label for="doc_levantamiento_necesidades_file" class="form-label">
     										<?php echo $proyecto->getDocLevantamientoNecesidades() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_levantamiento_necesidades_file"
     									   name="doc_levantamiento_necesidades_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-primary btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocLevantamientoNecesidades() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>

									<!-- Cotización -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-success text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-dollar-sign me-2"></i>
													Cotización
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocCotizacion()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocCotizacion()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocCotizacion()); ?>"
														   class="btn btn-sm btn-outline-success" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_cotizacion">

													<div class="mb-3">
     									<label for="doc_cotizacion_file" class="form-label">
     										<?php echo $proyecto->getDocCotizacion() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_cotizacion_file"
     									   name="doc_cotizacion_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-success btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocCotizacion() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>

									<!-- Presentación -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-info text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-presentation me-2"></i>
													Presentación
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocPresentacion()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocPresentacion()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocPresentacion()); ?>"
														   class="btn btn-sm btn-outline-info" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_presentacion">

													<div class="mb-3">
     									<label for="doc_presentacion_file" class="form-label">
     										<?php echo $proyecto->getDocPresentacion() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_presentacion_file"
     									   name="doc_presentacion_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-info btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocPresentacion() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: INICIO ?>

					<?php #region region TAB: PLANIFICACIÓN ?>
					<div class="tab-pane fade" id="v-pills-planificacion" role="tabpanel"
						 aria-labelledby="v-pills-planificacion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-calendar-alt fa-fw me-2"></i> Planificación
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Planificación - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: PLANIFICACIÓN ?>

					<?php #region region TAB: EJECUCIÓN ?>
					<div class="tab-pane fade" id="v-pills-ejecucion" role="tabpanel"
						 aria-labelledby="v-pills-ejecucion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-play-circle fa-fw me-2"></i> Ejecución
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Ejecución - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: EJECUCIÓN ?>

					<?php #region region TAB: MONITOREO Y CONTROL ?>
					<div class="tab-pane fade" id="v-pills-monitoreo" role="tabpanel"
						 aria-labelledby="v-pills-monitoreo-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-chart-line fa-fw me-2"></i> Monitoreo y Control
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Monitoreo y Control - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: MONITOREO Y CONTROL ?>

					<?php #region region TAB: CIERRE ?>
					<div class="tab-pane fade" id="v-pills-cierre" role="tabpanel"
						 aria-labelledby="v-pills-cierre-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-flag-checkered fa-fw me-2"></i> Cierre
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Cierre - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: CIERRE ?>
					
				</div>
			</div>
		</div>
		<?php #endregion PROJECT MANAGEMENT CONTENT ?>
		<?php endif; ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top">
		<i class="fa fa-angle-up"></i>
	</a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
	// Initialize any JavaScript functionality here
	console.log('Project Management page loaded successfully');

	// File upload validation
	const documentUploadForms = document.querySelectorAll('.document-upload-form');

	documentUploadForms.forEach(function(form) {
		form.addEventListener('submit', function(event) {
			const fileInput = form.querySelector('input[type="file"]');
			const submitBtn = form.querySelector('button[type="submit"]');

			// Reset validation styling
			fileInput.classList.remove('is-invalid');

			// Check if a file is selected
			if (!fileInput.files || fileInput.files.length === 0) {
				event.preventDefault();
				fileInput.classList.add('is-invalid');

				// Show SweetAlert error
				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'Debe seleccionar un archivo PDF para subir.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('Debe seleccionar un archivo PDF para subir.');
				}
				return false;
			}

			const file = fileInput.files[0];

			// Validate file type
			if (file.type !== 'application/pdf') {
				event.preventDefault();
				fileInput.classList.add('is-invalid');

				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'Solo se permiten archivos PDF. Por favor, seleccione un archivo PDF válido.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('Solo se permiten archivos PDF. Por favor, seleccione un archivo PDF válido.');
				}
				return false;
			}

			// Validate file size (max 50MB)
			const maxSize = 50 * 1024 * 1024; // 50MB in bytes
			if (file.size > maxSize) {
				event.preventDefault();
				fileInput.classList.add('is-invalid');
				
				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'El archivo es demasiado grande. El tamaño máximo permitido es 50MB.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('El archivo es demasiado grande. El tamaño máximo permitido es 50MB.');
				}
				return false;
			}

			// If all validations pass, show loading indicator
			const originalText = submitBtn.innerHTML;
			submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Subiendo...';
			submitBtn.disabled = true;

			// Re-enable button after a timeout in case of server error
			setTimeout(function() {
				submitBtn.innerHTML = originalText;
				submitBtn.disabled = false;
			}, 30000); // 30 seconds timeout

			return true;
		});
	});
});
</script>
<?php #endregion JS ?>

</body>
</html>
