/**
 * State-based routing for AngularJS 1.x
 * This bundle requires the ui-router-core.js bundle from the @uirouter/core package.
 * @version v1.0.30
 * @link https://ui-router.github.io
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("angular"),require("@uirouter/core")):"function"==typeof define&&define.amd?define(["exports","angular","@uirouter/core"],t):t((e=e||self)["@uirouter/angularjs"]={},e.angular,e["@uirouter/core"])}(this,(function(e,t,r){"use strict";var n=angular,i=t&&t.module?t:n;function o(){var e=null;return function(t,n){return e=e||r.services.$injector.get("$templateFactory"),[new s(t,n,e)]}}var a=function(e,t){return e.reduce((function(e,n){return e||r.isDefined(t[n])}),!1)};function u(e){if(!e.parent)return{};var t=["component","bindings","componentProvider"],n=["templateProvider","templateUrl","template","notify","async"].concat(["controller","controllerProvider","controllerAs","resolveAs"]),i=t.concat(n);if(r.isDefined(e.views)&&a(i,e))throw new Error("State '"+e.name+"' has a 'views' object. It cannot also have \"view properties\" at the state level.  Move the following properties into a view (in the 'views' object):  "+i.filter((function(t){return r.isDefined(e[t])})).join(", "));var o={},u=e.views||{$default:r.pick(e,i)};return r.forEach(u,(function(i,u){if(u=u||"$default",r.isString(i)&&(i={component:i}),i=r.extend({},i),a(t,i)&&a(n,i))throw new Error("Cannot combine: "+t.join("|")+" with: "+n.join("|")+" in stateview: '"+u+"@"+e.name+"'");i.resolveAs=i.resolveAs||"$resolve",i.$type="ng1",i.$context=e,i.$name=u;var c=r.ViewService.normalizeUIViewTarget(i.$context,i.$name);i.$uiViewName=c.uiViewName,i.$uiViewContextAnchor=c.uiViewContextAnchor,o[u]=i})),o}var c=0,s=function(){function e(e,t,r){var n=this;this.path=e,this.viewDecl=t,this.factory=r,this.$id=c++,this.loaded=!1,this.getTemplate=function(e,t){return n.component?n.factory.makeComponentTemplate(e,t,n.component,n.viewDecl.bindings):n.template}}return e.prototype.load=function(){var e=this,t=r.services.$q,n=new r.ResolveContext(this.path),i=this.path.reduce((function(e,t){return r.extend(e,t.paramValues)}),{}),o={template:t.when(this.factory.fromConfig(this.viewDecl,i,n)),controller:t.when(this.getController(n))};return t.all(o).then((function(t){return r.trace.traceViewServiceEvent("Loaded",e),e.controller=t.controller,r.extend(e,t.template),e}))},e.prototype.getController=function(e){var t=this.viewDecl.controllerProvider;if(!r.isInjectable(t))return this.viewDecl.controller;var n=r.services.$injector.annotate(t),i=r.isArray(t)?r.tail(t):t;return new r.Resolvable("",i,n).get(e)},e}(),l=function(){function e(){var e=this;this._useHttp=i.version.minor<3,this.$get=["$http","$templateCache","$injector",function(t,r,n){return e.$templateRequest=n.has&&n.has("$templateRequest")&&n.get("$templateRequest"),e.$http=t,e.$templateCache=r,e}]}return e.prototype.useHttpService=function(e){this._useHttp=e},e.prototype.fromConfig=function(e,t,n){var i=function(e){return r.services.$q.when(e).then((function(e){return{template:e}}))},o=function(e){return r.services.$q.when(e).then((function(e){return{component:e}}))};return r.isDefined(e.template)?i(this.fromString(e.template,t)):r.isDefined(e.templateUrl)?i(this.fromUrl(e.templateUrl,t)):r.isDefined(e.templateProvider)?i(this.fromProvider(e.templateProvider,t,n)):r.isDefined(e.component)?o(e.component):r.isDefined(e.componentProvider)?o(this.fromComponentProvider(e.componentProvider,t,n)):i("<ui-view></ui-view>")},e.prototype.fromString=function(e,t){return r.isFunction(e)?e(t):e},e.prototype.fromUrl=function(e,t){return r.isFunction(e)&&(e=e(t)),null==e?null:this._useHttp?this.$http.get(e,{cache:this.$templateCache,headers:{Accept:"text/html"}}).then((function(e){return e.data})):this.$templateRequest(e)},e.prototype.fromProvider=function(e,t,n){var i=r.services.$injector.annotate(e),o=r.isArray(e)?r.tail(e):e;return new r.Resolvable("",o,i).get(n)},e.prototype.fromComponentProvider=function(e,t,n){var i=r.services.$injector.annotate(e),o=r.isArray(e)?r.tail(e):e;return new r.Resolvable("",o,i).get(n)},e.prototype.makeComponentTemplate=function(e,t,n,o){o=o||{};var a=i.version.minor>=3?"::":"",u=function(e){var t=r.kebobString(e);return/^(x|data)-/.exec(t)?"x-"+t:t},c=function(e){var t=r.services.$injector.get(e+"Directive");if(!t||!t.length)throw new Error("Unable to find component named '"+e+"'");return t.map(f).reduce(r.unnestR,[])}(n).map((function(n){var i=n.name,c=n.type,s=u(i);if(e.attr(s)&&!o[i])return s+"='"+e.attr(s)+"'";var l=o[i]||i;if("@"===c)return s+"='{{"+a+"$resolve."+l+"}}'";if("&"===c){var f=t.getResolvable(l),v=f&&f.data,p=v&&r.services.$injector.annotate(v)||[];return s+"='$resolve."+l+(r.isArray(v)?"["+(v.length-1)+"]":"")+"("+p.join(",")+")'"}return s+"='"+a+"$resolve."+l+"'"})).join(" "),s=u(n);return"<"+s+" "+c+"></"+s+">"},e}();var f=function(e){return r.isObject(e.bindToController)?v(e.bindToController):v(e.scope)},v=function(e){return Object.keys(e||{}).map((function(t){return[t,/^([=<@&])[?]?(.*)/.exec(e[t])]})).filter((function(e){return r.isDefined(e)&&r.isArray(e[1])})).map((function(e){return{name:e[1][2]||e[0],type:e[1][1]}}))},p=function(){function e(t,n){this.stateRegistry=t,this.stateService=n,r.createProxyFunctions(r.val(e.prototype),this,r.val(this))}return e.prototype.decorator=function(e,t){return this.stateRegistry.decorator(e,t)||this},e.prototype.state=function(e,t){return r.isObject(e)?t=e:t.name=e,this.stateRegistry.register(t),this},e.prototype.onInvalid=function(e){return this.stateService.onInvalid(e)},e}(),d=function(e){return function(t){var n=t[e],i="onExit"===e?"from":"to";return n?function(e,t){var o=new r.ResolveContext(e.treeChanges(i)).subContext(t.$$state()),a=r.extend(D(o),{$state$:t,$transition$:e});return r.services.$injector.invoke(n,this,a)}:void 0}},$=function(){function e(e){this._urlListeners=[],this.$locationProvider=e;var t=r.val(e);r.createProxyFunctions(t,this,t,["hashPrefix"])}return e.monkeyPatchPathParameterType=function(e){var t=e.urlMatcherFactory.type("path");t.encode=function(e){return null!=e?e.toString().replace(/(~|\/)/g,(function(e){return{"~":"~~","/":"~2F"}[e]})):e},t.decode=function(e){return null!=e?e.toString().replace(/(~~|~2F)/g,(function(e){return{"~~":"~","~2F":"/"}[e]})):e}},e.prototype.dispose=function(){},e.prototype.onChange=function(e){var t=this;return this._urlListeners.push(e),function(){return r.removeFrom(t._urlListeners)(e)}},e.prototype.html5Mode=function(){var e=this.$locationProvider.html5Mode();return(e=r.isObject(e)?e.enabled:e)&&this.$sniffer.history},e.prototype.baseHref=function(){return this._baseHref||(this._baseHref=this.$browser.baseHref()||this.$window.location.pathname)},e.prototype.url=function(e,t,n){return void 0===t&&(t=!1),r.isDefined(e)&&this.$location.url(e),t&&this.$location.replace(),n&&this.$location.state(n),this.$location.url()},e.prototype._runtimeServices=function(e,t,n,i,o){var a=this;this.$location=t,this.$sniffer=n,this.$browser=i,this.$window=o,e.$on("$locationChangeSuccess",(function(e){return a._urlListeners.forEach((function(t){return t(e)}))}));var u=r.val(t);r.createProxyFunctions(u,this,u,["replace","path","search","hash"]),r.createProxyFunctions(u,this,u,["port","protocol","host"])},e}(),h=function(){function e(e){this.router=e}return e.injectableHandler=function(e,t){return function(n){return r.services.$injector.invoke(t,null,{$match:n,$stateParams:e.globals.params})}},e.prototype.$get=function(){var e=this.router.urlService;return this.router.urlRouter.update(!0),e.interceptDeferred||e.listen(),this.router.urlRouter},e.prototype.rule=function(e){var t=this;if(!r.isFunction(e))throw new Error("'rule' must be a function");var n=new r.BaseUrlRule((function(){return e(r.services.$injector,t.router.locationService)}),r.identity);return this.router.urlService.rules.rule(n),this},e.prototype.otherwise=function(e){var t=this,n=this.router.urlService.rules;if(r.isString(e))n.otherwise(e);else{if(!r.isFunction(e))throw new Error("'rule' must be a string or function");n.otherwise((function(){return e(r.services.$injector,t.router.locationService)}))}return this},e.prototype.when=function(t,n){return(r.isArray(n)||r.isFunction(n))&&(n=e.injectableHandler(this.router,n)),this.router.urlService.rules.when(t,n),this},e.prototype.deferIntercept=function(e){this.router.urlService.deferIntercept(e)},e}();i.module("ui.router.angular1",[]);var m=i.module("ui.router.init",["ng"]),g=i.module("ui.router.util",["ui.router.init"]),w=i.module("ui.router.router",["ui.router.util"]),y=i.module("ui.router.state",["ui.router.router","ui.router.util","ui.router.angular1"]),S=i.module("ui.router",["ui.router.init","ui.router.state","ui.router.angular1"]),b=(i.module("ui.router.compat",["ui.router"]),null);function x(e){(b=this.router=new r.UIRouter).stateProvider=new p(b.stateRegistry,b.stateService),b.stateRegistry.decorator("views",u),b.stateRegistry.decorator("onExit",d("onExit")),b.stateRegistry.decorator("onRetain",d("onRetain")),b.stateRegistry.decorator("onEnter",d("onEnter")),b.viewService._pluginapi._viewConfigFactory("ng1",o()),b.urlService.config._decodeParams=!1;var t=b.locationService=b.locationConfig=new $(e);function n(e,r,n,i,o,a,u){return t._runtimeServices(o,e,i,r,n),delete b.router,delete b.$get,b}return $.monkeyPatchPathParameterType(b),b.router=b,b.$get=n,n.$inject=["$location","$browser","$window","$sniffer","$rootScope","$http","$templateCache"],b}x.$inject=["$locationProvider"];var C=function(e){return["$uiRouterProvider",function(t){var r=t.router[e];return r.$get=function(){return r},r}]};function R(e,t,n){if(r.services.$injector=e,r.services.$q=t,!Object.prototype.hasOwnProperty.call(e,"strictDi"))try{e.invoke((function(e){}))}catch(t){e.strictDi=!!/strict mode/.exec(t&&t.toString())}n.stateRegistry.get().map((function(e){return e.$$state().resolvables})).reduce(r.unnestR,[]).filter((function(e){return"deferred"===e.deps})).forEach((function(t){return t.deps=e.annotate(t.resolveFn,e.strictDi)}))}R.$inject=["$injector","$q","$uiRouter"];function P(e){e.$watch((function(){r.trace.approximateDigests++}))}P.$inject=["$rootScope"],m.provider("$uiRouter",x),w.provider("$urlRouter",["$uiRouterProvider",function(e){return e.urlRouterProvider=new h(e)}]),g.provider("$urlService",C("urlService")),g.provider("$urlMatcherFactory",["$uiRouterProvider",function(){return b.urlMatcherFactory}]),g.provider("$templateFactory",(function(){return new l})),y.provider("$stateRegistry",C("stateRegistry")),y.provider("$uiRouterGlobals",C("globals")),y.provider("$transitions",C("transitionService")),y.provider("$state",["$uiRouterProvider",function(){return r.extend(b.stateProvider,{$get:function(){return b.stateService}})}]),y.factory("$stateParams",["$uiRouter",function(e){return e.globals.params}]),S.factory("$view",(function(){return b.viewService})),S.service("$trace",(function(){return r.trace})),S.run(P),g.run(["$urlMatcherFactory",function(e){}]),y.run(["$state",function(e){}]),w.run(["$urlRouter",function(e){}]),m.run(R);var E,j,A,V,D=function(e){return e.getTokens().filter(r.isString).map((function(t){var r=e.getResolvable(t);return[t,"NOWAIT"===e.getPolicy(r).async?r.promise:r.data]})).reduce(r.applyPairs,{})};function F(e){var t=e.match(/^\s*({[^}]*})\s*$/);t&&(e="("+t[1]+")");var r=e.replace(/\n/g," ").match(/^\s*([^(]*?)\s*(\((.*)\))?\s*$/);if(!r||4!==r.length)throw new Error("Invalid state ref '"+e+"'");return{state:r[1]||null,paramExpr:r[3]||null}}function I(e){var t=e.parent().inheritedData("$uiView"),n=r.parse("$cfg.path")(t);return n?r.tail(n).state.name:void 0}function O(e,t,n){var i=n.uiState||e.current.name,o=r.extend(function(e,t){return{relative:I(e)||t.$current,inherit:!0,source:"sref"}}(t,e),n.uiStateOpts||{}),a=e.href(i,n.uiStateParams,o);return{uiState:i,uiStateParams:n.uiStateParams,uiStateOpts:o,href:a}}function q(e){var t="[object SVGAnimatedString]"===Object.prototype.toString.call(e.prop("href")),r="FORM"===e[0].nodeName;return{attr:r?"action":t?"xlink:href":"href",isAnchor:"A"===e.prop("tagName").toUpperCase(),clickable:!r}}function U(e,t,r,n,i){return function(o){var a=o.which||o.button,u=i();if(!(a>1||o.ctrlKey||o.metaKey||o.shiftKey||o.altKey||e.attr("target"))){var c=r((function(){e.attr("disabled")||t.go(u.uiState,u.uiStateParams,u.uiStateOpts)}));o.preventDefault();var s=n.isAnchor&&!u.href?1:0;o.preventDefault=function(){s--<=0&&r.cancel(c)}}}}function _(e,t,n,i){var o;i&&(o=i.events),r.isArray(o)||(o=["click"]);for(var a=e.on?"on":"bind",u=0,c=o;u<c.length;u++){var s=c[u];e[a](s,n)}t.$on("$destroy",(function(){for(var t=e.off?"off":"unbind",r=0,i=o;r<i.length;r++){var a=i[r];e[t](a,n)}}))}function k(e){var t=function(t,r,n){return e.is(t,r,n)};return t.$stateful=!0,t}function L(e){var t=function(t,r,n){return e.includes(t,r,n)};return t.$stateful=!0,t}function T(e,t,n,o,a){var u=r.parse("viewDecl.controllerAs"),c=r.parse("viewDecl.resolveAs");return{restrict:"ECA",priority:-400,compile:function(o){var s=o.html();return o.empty(),function(o,l){var f=l.data("$uiView");if(!f)return l.html(s),void e(l.contents())(o);var v=f.$cfg||{viewDecl:{},getTemplate:r.noop},p=v.path&&new r.ResolveContext(v.path);l.html(v.getTemplate(l,p)||s),r.trace.traceUIViewFill(f.$uiView,l.html());var d=e(l.contents()),$=v.controller,h=u(v),m=c(v),g=p&&D(p);if(o[m]=g,$){var w=t($,r.extend({},g,{$scope:o,$element:l}));h&&(o[h]=w,o[h][m]=g),l.data("$ngControllerController",w),l.children().data("$ngControllerController",w),N(a,n,w,o,v)}if(r.isString(v.component))var y=r.kebobString(v.component),S=new RegExp("^(x-|data-)?"+y+"$","i"),b=o.$watch((function(){var e=[].slice.call(l[0].children).filter((function(e){return e&&e.tagName&&S.exec(e.tagName)}));return e&&i.element(e).data("$"+v.component+"Controller")}),(function(e){e&&(N(a,n,e,o,v),b())}));d(o)}}}}E=["$uiRouter","$timeout",function(e,t){var n=e.stateService;return{restrict:"A",require:["?^uiSrefActive","?^uiSrefActiveEq"],link:function(i,o,a,u){var c=q(o),s=u[1]||u[0],l=null,f={},v=function(){return O(n,o,f)},p=F(a.uiSref);function d(){var e=v();l&&l(),s&&(l=s.$$addStateInfo(e.uiState,e.uiStateParams)),null!=e.href&&a.$set(c.attr,e.href)}if(f.uiState=p.state,f.uiStateOpts=a.uiSrefOpts?i.$eval(a.uiSrefOpts):{},p.paramExpr&&(i.$watch(p.paramExpr,(function(e){f.uiStateParams=r.extend({},e),d()}),!0),f.uiStateParams=r.extend({},i.$eval(p.paramExpr))),d(),i.$on("$destroy",e.stateRegistry.onStatesChanged(d)),i.$on("$destroy",e.transitionService.onSuccess({},d)),c.clickable){var $=U(o,n,t,c,v);_(o,i,$,f.uiStateOpts)}}}}],j=["$uiRouter","$timeout",function(e,t){var n=e.stateService;return{restrict:"A",require:["?^uiSrefActive","?^uiSrefActiveEq"],link:function(i,o,a,u){var c,s=q(o),l=u[1]||u[0],f=null,v={},p=function(){return O(n,o,v)},d=["uiState","uiStateParams","uiStateOpts"],$=d.reduce((function(e,t){return e[t]=r.noop,e}),{});function h(){var e=p();f&&f(),l&&(f=l.$$addStateInfo(e.uiState,e.uiStateParams)),null!=e.href&&a.$set(s.attr,e.href)}d.forEach((function(e){v[e]=a[e]?i.$eval(a[e]):null,a.$observe(e,(function(t){$[e](),$[e]=i.$watch(t,(function(t){v[e]=t,h()}),!0)}))})),h(),i.$on("$destroy",e.stateRegistry.onStatesChanged(h)),i.$on("$destroy",e.transitionService.onSuccess({},h)),s.clickable&&(c=U(o,n,t,s,p),_(o,i,c,v.uiStateOpts))}}}],A=["$state","$stateParams","$interpolate","$uiRouter",function(e,t,n,i){return{restrict:"A",controller:["$scope","$element","$attrs",function(t,o,a){var u,c,s,l,f,v=[];u=n(a.uiSrefActiveEq||"",!1)(t);try{c=t.$eval(a.uiSrefActive)}catch(e){}function p(e){e.promise.then(m,r.noop)}function d(){$(c)}function $(e){r.isObject(e)&&(v=[],r.forEach(e,(function(e,n){var i=function(e,r){var n=F(e);h(n.state,t.$eval(n.paramExpr),r)};r.isString(e)?i(e,n):r.isArray(e)&&r.forEach(e,(function(e){i(e,n)}))})))}function h(t,n,i){var a={state:e.get(t,I(o))||{name:t},params:n,activeClass:i};return v.push(a),function(){r.removeFrom(v)(a)}}function m(){var n=function(e){return e.split(/\s/).filter(r.identity)},i=function(e){return e.map((function(e){return e.activeClass})).map(n).reduce(r.unnestR,[])},a=i(v).concat(n(u)).reduce(r.uniqR,[]),c=i(v.filter((function(t){return e.includes(t.state.name,t.params)}))),s=!!v.filter((function(t){return e.is(t.state.name,t.params)})).length?n(u):[],l=c.concat(s).reduce(r.uniqR,[]),f=a.filter((function(e){return!r.inArray(l,e)}));t.$evalAsync((function(){l.forEach((function(e){return o.addClass(e)})),f.forEach((function(e){return o.removeClass(e)}))}))}$(c=c||n(a.uiSrefActive||"",!1)(t)),this.$$addStateInfo=function(e,t){if(!(r.isObject(c)&&v.length>0)){var n=h(e,t,c);return m(),n}},t.$on("$destroy",(s=i.stateRegistry.onStatesChanged(d),l=i.transitionService.onStart({},p),f=t.$on("$stateChangeSuccess",m),function(){s(),l(),f()})),i.globals.transition&&p(i.globals.transition),m()}]}}],i.module("ui.router.state").directive("uiSref",E).directive("uiSrefActive",A).directive("uiSrefActiveEq",A).directive("uiState",j),k.$inject=["$state"],L.$inject=["$state"],i.module("ui.router.state").filter("isState",k).filter("includedByState",L),V=["$view","$animate","$uiViewScroll","$interpolate","$q",function(e,t,n,o,a){var u={$cfg:{viewDecl:{$context:e._pluginapi._rootViewContext()}},$uiView:{}},c={count:0,restrict:"ECA",terminal:!0,priority:400,transclude:"element",compile:function(l,f,v){return function(l,f,p){var d,$,h,m,g=p.onload||"",w=p.autoscroll,y={enter:function(e,r,n){i.version.minor>2?t.enter(e,null,r).then(n):t.enter(e,null,r,n)},leave:function(e,r){i.version.minor>2?t.leave(e).then(r):t.leave(e,r)}},S=f.inheritedData("$uiView")||u,b=o(p.uiView||p.name||"")(l)||"$default",x={$type:"ng1",id:c.count++,name:b,fqn:S.$uiView.fqn?S.$uiView.fqn+"."+b:b,config:null,configUpdated:function(e){if(e&&!(e instanceof s))return;if(t=m,n=e,t===n)return;var t,n;r.trace.traceUIViewConfigUpdated(x,e&&e.viewDecl&&e.viewDecl.$context),m=e,R(e)},get creationContext(){var e=r.parse("$cfg.viewDecl.$context")(S),t=r.parse("$uiView.creationContext")(S);return e||t}};r.trace.traceUIViewEvent("Linking",x),f.data("$uiView",{$uiView:x}),R();var C=e.registerUIView(x);function R(e){var t=l.$new(),i=a.defer(),o=a.defer(),u={$cfg:e,$uiView:x},c={$animEnter:i.promise,$animLeave:o.promise,$$animLeave:o};t.$emit("$viewContentLoading",b);var s=v(t,(function(e){e.data("$uiViewAnim",c),e.data("$uiView",u),y.enter(e,f,(function(){i.resolve(),h&&h.$emit("$viewContentAnimationEnded"),(r.isDefined(w)&&!w||l.$eval(w))&&n(e)})),function(){if(d&&(r.trace.traceUIViewEvent("Removing (previous) el",d.data("$uiView")),d.remove(),d=null),h&&(r.trace.traceUIViewEvent("Destroying scope",x),h.$destroy(),h=null),$){var e=$.data("$uiViewAnim");r.trace.traceUIViewEvent("Animate out",e),y.leave($,(function(){e.$$animLeave.resolve(),d=null})),d=$,$=null}}()}));$=s,(h=t).$emit("$viewContentLoaded",e||m),h.$eval(g)}l.$on("$destroy",(function(){r.trace.traceUIViewEvent("Destroying/Unregistering",x),C()}))}}};return c}],T.$inject=["$compile","$controller","$transitions","$view","$q"];var H="function"==typeof i.module("ui.router").component,M=0;function N(e,t,n,i,o){!r.isFunction(n.$onInit)||(o.viewDecl.component||o.viewDecl.componentProvider)&&H||n.$onInit();var a=r.tail(o.path).state.self,u={bind:n};if(r.isFunction(n.uiOnParamsChanged)){var c=new r.ResolveContext(o.path).getResolvable("$transition$").data;i.$on("$destroy",t.onSuccess({},(function(e){if(e!==c&&-1===e.exiting().indexOf(a)){var t=e.params("to"),i=e.params("from"),o=function(e){return e.paramSchema},u=e.treeChanges("to").map(o).reduce(r.unnestR,[]),s=e.treeChanges("from").map(o).reduce(r.unnestR,[]),l=u.filter((function(e){var r=s.indexOf(e);return-1===r||!s[r].type.equals(t[e.id],i[e.id])}));if(l.length){var f=l.map((function(e){return e.id})),v=r.filter(t,(function(e,t){return-1!==f.indexOf(t)}));n.uiOnParamsChanged(v,e)}}}),u))}if(r.isFunction(n.uiCanExit)){var s=M++,l=function(e){return!!e&&(e._uiCanExitIds&&!0===e._uiCanExitIds[s]||l(e.redirectedFrom()))},f={exiting:a.name};i.$on("$destroy",t.onBefore(f,(function(t){var r,i=t._uiCanExitIds=t._uiCanExitIds||{};return l(t)||(r=e.when(n.uiCanExit(t))).then((function(e){return i[s]=!1!==e})),r}),u))}}i.module("ui.router.state").directive("uiView",V),i.module("ui.router.state").directive("uiView",T),i.module("ui.router.state").provider("$uiViewScroll",(function(){var e=!1;this.useAnchorScroll=function(){e=!0},this.$get=["$anchorScroll","$timeout",function(t,r){return e?t:function(e){return r((function(){e[0].scrollIntoView()}),0,!1)}}]}));Object.keys(r).forEach((function(t){"default"!==t&&Object.defineProperty(e,t,{enumerable:!0,get:function(){return r[t]}})})),e.core=r,e.Ng1ViewConfig=s,e.StateProvider=p,e.UrlRouterProvider=h,e.default="ui.router",e.getLocals=D,e.getNg1ViewConfigFactory=o,e.ng1ViewsBuilder=u,e.watchDigests=P,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=ui-router-angularjs.min.js.map
