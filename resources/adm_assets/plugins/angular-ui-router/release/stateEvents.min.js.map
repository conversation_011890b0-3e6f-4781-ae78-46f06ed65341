{"version": 3, "file": "stateEvents.min.js", "sources": ["angular-ui-router/src/angular.ts", "angular-ui-router/src/legacy/stateEvents.ts"], "sourcesContent": ["/** @publicapi @module ng1 */ /** */\nimport * as ng_from_import from 'angular';\n/** @hidden */ declare let angular;\n/** @hidden */ const ng_from_global = angular;\n/** @hidden */ export const ng = ng_from_import && ng_from_import.module ? ng_from_import : ng_from_global;\n", "/**\n * # Legacy state events\n *\n * Polyfill implementation of the UI-Router 0.2.x state events.\n *\n * The 0.2.x state events are deprecated.  We recommend moving to Transition Hooks instead, as they\n * provide much more flexibility, support async, and provide the context (the Transition, etc) necessary\n * to implement meaningful application behaviors.\n *\n * To enable these state events, include the `stateEvents.js` file in your project, e.g.,\n * ```\n * <script src=\"stateEvents.js\"></script>\n * ```\n * and also make sure you depend on the `ui.router.state.events` angular module, e.g.,\n * ```\n * angular.module(\"myApplication\", ['ui.router', 'ui.router.state.events']\n * ```\n *\n * @publicapi @module ng1_state_events\n */ /** */\nimport { ng as angular } from '../angular';\nimport { IScope, IAngularEvent, IServiceProviderFactory } from 'angular';\nimport {\n  Obj,\n  TargetState,\n  StateService,\n  Transition,\n  TransitionService,\n  UrlRouter,\n  HookResult,\n  UIInjector,\n} from '@uirouter/core';\nimport { StateProvider } from '../stateProvider';\n\n/**\n * An event broadcast on `$rootScope` when the state transition **begins**.\n *\n * ### Deprecation warning: use [[TransitionService.onStart]] instead\n *\n * You can use `event.preventDefault()`\n * to prevent the transition from happening and then the transition promise will be\n * rejected with a `'transition prevented'` value.\n *\n * Additional arguments to the event handler are provided:\n * - `toState`: the Transition Target state\n * - `toParams`: the Transition Target Params\n * - `fromState`: the state the transition is coming from\n * - `fromParams`: the parameters from the state the transition is coming from\n * - `options`: any Transition Options\n * - `$transition$`: the [[Transition]]\n *\n * #### Example:\n * ```js\n * $rootScope.$on('$stateChangeStart', function(event, transition) {\n *   event.preventDefault();\n *   // transitionTo() promise will be rejected with\n *   // a 'transition prevented' error\n * })\n * ```\n *\n * @event $stateChangeStart\n * @deprecated\n */\nexport let $stateChangeStart: IAngularEvent;\n\n/**\n * An event broadcast on `$rootScope` if a transition is **cancelled**.\n *\n * ### Deprecation warning: use [[TransitionService.onStart]] instead\n *\n * Additional arguments to the event handler are provided:\n * - `toState`: the Transition Target state\n * - `toParams`: the Transition Target Params\n * - `fromState`: the state the transition is coming from\n * - `fromParams`: the parameters from the state the transition is coming from\n * - `options`: any Transition Options\n * - `$transition$`: the [[Transition]] that was cancelled\n *\n * @event $stateChangeCancel\n * @deprecated\n */\nexport let $stateChangeCancel: IAngularEvent;\n\n/**\n * An event broadcast on `$rootScope` once the state transition is **complete**.\n *\n * ### Deprecation warning: use [[TransitionService.onStart]] and [[Transition.promise]], or [[Transition.onSuccess]]\n *\n * Additional arguments to the event handler are provided:\n * - `toState`: the Transition Target state\n * - `toParams`: the Transition Target Params\n * - `fromState`: the state the transition is coming from\n * - `fromParams`: the parameters from the state the transition is coming from\n * - `options`: any Transition Options\n * - `$transition$`: the [[Transition]] that just succeeded\n *\n * @event $stateChangeSuccess\n * @deprecated\n */\nexport let $stateChangeSuccess: IAngularEvent;\n\n/**\n * An event broadcast on `$rootScope` when an **error occurs** during transition.\n *\n * ### Deprecation warning: use [[TransitionService.onStart]] and [[Transition.promise]], or [[Transition.onError]]\n *\n * It's important to note that if you\n * have any errors in your resolve functions (javascript errors, non-existent services, etc)\n * they will not throw traditionally. You must listen for this $stateChangeError event to\n * catch **ALL** errors.\n *\n * Additional arguments to the event handler are provided:\n * - `toState`: the Transition Target state\n * - `toParams`: the Transition Target Params\n * - `fromState`: the state the transition is coming from\n * - `fromParams`: the parameters from the state the transition is coming from\n * - `error`: The reason the transition errored.\n * - `options`: any Transition Options\n * - `$transition$`: the [[Transition]] that errored\n *\n * @event $stateChangeError\n * @deprecated\n */\nexport let $stateChangeError: IAngularEvent;\n\n/**\n * An event broadcast on `$rootScope` when a requested state **cannot be found** using the provided state name.\n *\n * ### Deprecation warning: use [[StateService.onInvalid]] instead\n *\n * The event is broadcast allowing any handlers a single chance to deal with the error (usually by\n * lazy-loading the unfound state). A `TargetState` object is passed to the listener handler,\n * you can see its properties in the example. You can use `event.preventDefault()` to abort the\n * transition and the promise returned from `transitionTo()` will be rejected with a\n * `'transition aborted'` error.\n *\n * Additional arguments to the event handler are provided:\n * - `unfoundState` Unfound State information. Contains: `to, toParams, options` properties.\n * - `fromState`: the state the transition is coming from\n * - `fromParams`: the parameters from the state the transition is coming from\n * - `options`: any Transition Options\n *\n * #### Example:\n * ```js\n * // somewhere, assume lazy.state has not been defined\n * $state.go(\"lazy.state\", { a: 1, b: 2 }, { inherit: false });\n *\n * // somewhere else\n * $scope.$on('$stateNotFound', function(event, transition) {\n * function(event, unfoundState, fromState, fromParams){\n *     console.log(unfoundState.to); // \"lazy.state\"\n *     console.log(unfoundState.toParams); // {a:1, b:2}\n *     console.log(unfoundState.options); // {inherit:false} + default options\n * });\n * ```\n *\n * @event $stateNotFound\n * @deprecated\n */\nexport let $stateNotFound: IAngularEvent;\n\n(function () {\n  const { isFunction, isString } = angular;\n\n  function applyPairs(memo: Obj, keyValTuple: any[]) {\n    let key: string, value: any;\n    if (Array.isArray(keyValTuple)) [key, value] = keyValTuple;\n    if (!isString(key)) throw new Error('invalid parameters to applyPairs');\n    memo[key] = value;\n    return memo;\n  }\n\n  function stateChangeStartHandler($transition$: Transition) {\n    if (!$transition$.options().notify || !$transition$.valid() || $transition$.ignored()) return;\n\n    const $injector = $transition$.injector();\n    const $stateEvents = $injector.get('$stateEvents');\n    const $rootScope = $injector.get('$rootScope');\n    const $state = $injector.get('$state');\n    const $urlRouter = $injector.get('$urlRouter');\n\n    const enabledEvents = $stateEvents.provider.enabled();\n\n    const toParams = $transition$.params('to');\n    const fromParams = $transition$.params('from');\n\n    if (enabledEvents.$stateChangeSuccess) {\n      const startEvent = $rootScope.$broadcast(\n        '$stateChangeStart',\n        $transition$.to(),\n        toParams,\n        $transition$.from(),\n        fromParams,\n        $transition$.options(),\n        $transition$\n      );\n\n      if (startEvent.defaultPrevented) {\n        if (enabledEvents.$stateChangeCancel) {\n          $rootScope.$broadcast(\n            '$stateChangeCancel',\n            $transition$.to(),\n            toParams,\n            $transition$.from(),\n            fromParams,\n            $transition$.options(),\n            $transition$\n          );\n        }\n        // Don't update and resync url if there's been a new transition started. see issue #2238, #600\n        if ($state.transition == null) $urlRouter.update();\n        return false;\n      }\n\n      // right after global state is updated\n      const successOpts = { priority: 9999 };\n      $transition$.onSuccess(\n        {},\n        function () {\n          $rootScope.$broadcast(\n            '$stateChangeSuccess',\n            $transition$.to(),\n            toParams,\n            $transition$.from(),\n            fromParams,\n            $transition$.options(),\n            $transition$\n          );\n        },\n        successOpts\n      );\n    }\n\n    if (enabledEvents.$stateChangeError) {\n      $transition$.promise['catch'](function (error) {\n        if (error && (error.type === 2 /* RejectType.SUPERSEDED */ || error.type === 3) /* RejectType.ABORTED */)\n          return;\n\n        const evt = $rootScope.$broadcast(\n          '$stateChangeError',\n          $transition$.to(),\n          toParams,\n          $transition$.from(),\n          fromParams,\n          error,\n          $transition$.options(),\n          $transition$\n        );\n\n        if (!evt.defaultPrevented) {\n          $urlRouter.update();\n        }\n      });\n    }\n  }\n\n  stateNotFoundHandler.$inject = ['$to$', '$from$', '$state', '$rootScope', '$urlRouter'];\n  function stateNotFoundHandler($to$: TargetState, $from$: TargetState, injector: UIInjector): HookResult {\n    const $state: StateService = injector.get('$state');\n    const $rootScope: IScope = injector.get('$rootScope');\n    const $urlRouter: UrlRouter = injector.get('$urlRouter');\n\n    interface StateNotFoundEvent extends IAngularEvent {\n      retry: Promise<any>;\n    }\n\n    const redirect = { to: $to$.identifier(), toParams: $to$.params(), options: $to$.options() };\n    const e = <StateNotFoundEvent>$rootScope.$broadcast('$stateNotFound', redirect, $from$.state(), $from$.params());\n\n    if (e.defaultPrevented || e.retry) $urlRouter.update();\n\n    function redirectFn(): TargetState {\n      return $state.target(redirect.to, redirect.toParams, redirect.options);\n    }\n\n    if (e.defaultPrevented) {\n      return false;\n    } else if (e.retry || !!$state.get(redirect.to)) {\n      return e.retry && isFunction(e.retry.then) ? e.retry.then(redirectFn) : redirectFn();\n    }\n  }\n\n  $StateEventsProvider.$inject = ['$stateProvider'];\n  function $StateEventsProvider($stateProvider: StateProvider) {\n    $StateEventsProvider.prototype.instance = this;\n\n    interface IEventsToggle {\n      [key: string]: boolean;\n      $stateChangeStart: boolean;\n      $stateNotFound: boolean;\n      $stateChangeSuccess: boolean;\n      $stateChangeError: boolean;\n    }\n\n    let runtime = false;\n    const allEvents = ['$stateChangeStart', '$stateNotFound', '$stateChangeSuccess', '$stateChangeError'];\n    const enabledStateEvents = <IEventsToggle>allEvents.map((e) => [e, true]).reduce(applyPairs, {});\n\n    function assertNotRuntime() {\n      if (runtime) throw new Error('Cannot enable events at runtime (use $stateEventsProvider');\n    }\n\n    /**\n     * Enables the deprecated UI-Router 0.2.x State Events\n     * [ '$stateChangeStart', '$stateNotFound', '$stateChangeSuccess', '$stateChangeError' ]\n     */\n    this.enable = function (...events: string[]) {\n      assertNotRuntime();\n      if (!events || !events.length) events = allEvents;\n      events.forEach((event) => (enabledStateEvents[event] = true));\n    };\n\n    /**\n     * Disables the deprecated UI-Router 0.2.x State Events\n     * [ '$stateChangeStart', '$stateNotFound', '$stateChangeSuccess', '$stateChangeError' ]\n     */\n    this.disable = function (...events: string[]) {\n      assertNotRuntime();\n      if (!events || !events.length) events = allEvents;\n      events.forEach((event) => delete enabledStateEvents[event]);\n    };\n\n    this.enabled = () => enabledStateEvents;\n\n    this.$get = $get;\n    $get.$inject = ['$transitions'];\n    function $get($transitions: TransitionService) {\n      runtime = true;\n\n      if (enabledStateEvents['$stateNotFound']) $stateProvider.onInvalid(stateNotFoundHandler);\n      if (enabledStateEvents.$stateChangeStart) $transitions.onBefore({}, stateChangeStartHandler, { priority: 1000 });\n\n      return {\n        provider: $StateEventsProvider.prototype.instance,\n      };\n    }\n  }\n\n  angular\n    .module('ui.router.state.events', ['ui.router.state'])\n    .provider('$stateEvents', ($StateEventsProvider as any) as IServiceProviderFactory)\n    .run([\n      '$stateEvents',\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      function ($stateEvents: any) {\n        /* Invokes $get() */\n      },\n    ]);\n})();\n"], "names": ["ng_from_global", "angular", "ng", "ng_from_import", "ng_from_import.module", "isFunction", "isString", "applyPairs", "memo", "key<PERSON>al<PERSON><PERSON><PERSON>", "key", "value", "Array", "isArray", "Error", "stateChangeStartHandler", "$transition$", "options", "notify", "valid", "ignored", "$injector", "injector", "$stateEvents", "get", "$rootScope", "$state", "$urlRouter", "enabledEvents", "provider", "enabled", "toParams", "params", "fromParams", "$stateChangeSuccess", "$broadcast", "to", "from", "defaultPrevented", "$stateChangeCancel", "transition", "update", "onSuccess", "priority", "$stateChangeError", "promise", "error", "type", "stateNotFoundHandler", "$to$", "$from$", "redirect", "identifier", "e", "state", "redirectFn", "target", "retry", "then", "$StateEventsProvider", "$stateProvider", "prototype", "instance", "this", "runtime", "allEvents", "enabledStateEvents", "map", "reduce", "assertNotRuntime", "$get", "$transitions", "onInvalid", "$stateChangeStart", "onBefore", "enable", "_i", "events", "length", "for<PERSON>ach", "event", "disable", "$inject", "module", "run", "$stateNotFound"], "mappings": ";;;;;;0QAGe,IAAMA,EAAiBC,QACVC,EAAKC,GAAkBC,SAAwBD,EAAiBH,GC6J5F,WACU,IAAAK,EAAyBJ,aAAbK,EAAaL,WAEjC,SAASM,EAAWC,EAAWC,GAC7B,IAAIC,EAAaC,EAEjB,GADIC,MAAMC,QAAQJ,KAAeC,EAAcD,KAATE,EAASF,OAC1CH,EAASI,GAAM,MAAM,IAAII,MAAM,oCAEpC,OADAN,EAAKE,GAAOC,EACLH,EAGT,SAASO,EAAwBC,GAC/B,GAAKA,EAAaC,UAAUC,QAAWF,EAAaG,UAAWH,EAAaI,UAA5E,CAEA,IAAMC,EAAYL,EAAaM,WACzBC,EAAeF,EAAUG,IAAI,gBAC7BC,EAAaJ,EAAUG,IAAI,cAC3BE,EAASL,EAAUG,IAAI,UACvBG,EAAaN,EAAUG,IAAI,cAE3BI,EAAgBL,EAAaM,SAASC,UAEtCC,EAAWf,EAAagB,OAAO,MAC/BC,EAAajB,EAAagB,OAAO,QAEvC,GAAIJ,EAAcM,oBAAqB,CAWrC,GAVmBT,EAAWU,WAC5B,oBACAnB,EAAaoB,KACbL,EACAf,EAAaqB,OACbJ,EACAjB,EAAaC,UACbD,GAGasB,iBAcb,OAbIV,EAAcW,oBAChBd,EAAWU,WACT,qBACAnB,EAAaoB,KACbL,EACAf,EAAaqB,OACbJ,EACAjB,EAAaC,UACbD,GAIqB,MAArBU,EAAOc,YAAoBb,EAAWc,UACnC,EAKTzB,EAAa0B,UACX,IACA,WACEjB,EAAWU,WACT,sBACAnB,EAAaoB,KACbL,EACAf,EAAaqB,OACbJ,EACAjB,EAAaC,UACbD,KAXc,CAAE2B,SAAU,OAkB9Bf,EAAcgB,mBAChB5B,EAAa6B,QAAe,OAAE,SAAUC,KAClCA,GAAyB,IAAfA,EAAMC,MAAyD,IAAfD,EAAMC,QAGxDtB,EAAWU,WACrB,oBACAnB,EAAaoB,KACbL,EACAf,EAAaqB,OACbJ,EACAa,EACA9B,EAAaC,UACbD,GAGOsB,kBACPX,EAAWc,cAOnB,SAASO,EAAqBC,EAAmBC,EAAqB5B,GACpE,IAAMI,EAAuBJ,EAASE,IAAI,UACpCC,EAAqBH,EAASE,IAAI,cAClCG,EAAwBL,EAASE,IAAI,cAMrC2B,EAAW,CAAEf,GAAIa,EAAKG,aAAcrB,SAAUkB,EAAKjB,SAAUf,QAASgC,EAAKhC,WAC3EoC,EAAwB5B,EAAWU,WAAW,iBAAkBgB,EAAUD,EAAOI,QAASJ,EAAOlB,UAIvG,SAASuB,IACP,OAAO7B,EAAO8B,OAAOL,EAASf,GAAIe,EAASpB,SAAUoB,EAASlC,SAGhE,OANIoC,EAAEf,kBAAoBe,EAAEI,QAAO9B,EAAWc,UAM1CY,EAAEf,mBAEKe,EAAEI,OAAW/B,EAAOF,IAAI2B,EAASf,IACnCiB,EAAEI,OAASpD,EAAWgD,EAAEI,MAAMC,MAAQL,EAAEI,MAAMC,KAAKH,GAAcA,SADnE,GAMT,SAASI,EAAqBC,GAC5BD,EAAqBE,UAAUC,SAAWC,KAU1C,IAAIC,GAAU,EACRC,EAAY,CAAC,oBAAqB,iBAAkB,sBAAuB,qBAC3EC,EAAoCD,EAAUE,KAAI,SAACd,GAAM,MAAA,CAACA,GAAG,MAAOe,OAAO7D,EAAY,IAE7F,SAAS8D,IACP,GAAIL,EAAS,MAAM,IAAIlD,MAAM,6DA2B/B,SAASwD,EAAKC,GAMZ,OALAP,GAAU,EAENE,EAAmC,gBAAGN,EAAeY,UAAUxB,GAC/DkB,EAAmBO,mBAAmBF,EAAaG,SAAS,GAAI3D,EAAyB,CAAE4B,SAAU,MAElG,CACLd,SAAU8B,EAAqBE,UAAUC,UA3B7CC,KAAKY,OAAS,eAAU,aAAAC,mBAAAA,IAAAC,kBACtBR,IACKQ,GAAWA,EAAOC,SAAQD,EAASZ,GACxCY,EAAOE,SAAQ,SAACC,GAAU,OAACd,EAAmBc,IAAS,MAOzDjB,KAAKkB,QAAU,eAAU,aAAAL,mBAAAA,IAAAC,kBACvBR,IACKQ,GAAWA,EAAOC,SAAQD,EAASZ,GACxCY,EAAOE,SAAQ,SAACC,GAAU,cAAOd,EAAmBc,OAGtDjB,KAAKjC,QAAU,WAAM,OAAAoC,GAErBH,KAAKO,KAAOA,EACZA,EAAKY,QAAU,CAAC,gBArElBlC,EAAqBkC,QAAU,CAAC,OAAQ,SAAU,SAAU,aAAc,cA0B1EvB,EAAqBuB,QAAU,CAAC,kBAwDhCjF,EACGkF,OAAO,yBAA0B,CAAC,oBAClCtD,SAAS,eAAiB8B,GAC1ByB,IAAI,CACH,eAEA,SAAU7D,OAvLhB,6BAhFWgB,2BA0CAK,2BA5DA6B,6BAoCAvC,wBA4DAmD"}