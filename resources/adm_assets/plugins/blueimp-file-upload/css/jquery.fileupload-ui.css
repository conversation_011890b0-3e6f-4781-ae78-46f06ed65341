@charset "UTF-8";
/*
 * jQuery File Upload UI Plugin CSS
 * https://github.com/blueimp/jQuery-File-Upload
 *
 * Copyright 2010, <PERSON>
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * https://opensource.org/licenses/MIT
 */

.progress-animated .progress-bar,
.progress-animated .bar {
  background: url('../img/progressbar.gif') !important;
  filter: none;
}
.fileupload-process {
  float: right;
  display: none;
}
.fileupload-processing .fileupload-process,
.files .processing .preview {
  display: block;
  width: 32px;
  height: 32px;
  background: url('../img/loading.gif') center no-repeat;
  background-size: contain;
}
.files audio,
.files video {
  max-width: 300px;
}
.files .name {
  word-wrap: break-word;
  overflow-wrap: anywhere;
  -webkit-hyphens: auto;
  hyphens: auto;
}
.files button {
  margin-bottom: 5px;
}
.toggle[type='checkbox'] {
  transform: scale(2);
  margin-left: 10px;
}

@media (max-width: 767px) {
  .fileupload-buttonbar .btn {
    margin-bottom: 5px;
  }
  .fileupload-buttonbar .delete,
  .fileupload-buttonbar .toggle,
  .files .toggle,
  .files .btn span {
    display: none;
  }
  .files audio,
  .files video {
    max-width: 80px;
  }
}

@media (max-width: 480px) {
  .files .image td:nth-child(2) {
    display: none;
  }
}
