<p align="center">
  <a href="https://perfectscrollbar.com/?utm_source=GitHub&utm_medium=PerfectScrollbar">
    <img src="https://perfectscrollbar.com/logo2.png" width="300" height="200">
  </a>
</p>

<h1 align="center">perfect-scrollbar</h1>

<p align="center">Minimalistic but perfect custom scrollbar plugin<p>


<p align="center">	
  <a href="https://npmcharts.com/compare/perfect-scrollbar?minimal=true"><img src="https://img.shields.io/npm/dm/perfect-scrollbar.svg" alt="Downloads"></a>
  <a href="https://github.com/mdbootstrap/bootstrap-material-design/blob/master/License.pdf"><img src="https://img.shields.io/badge/license-MIT-green.svg" alt="License"></a>	
  <a href="https://badge.fury.io/js/perfect-scrollbar"><img src="https://badge.fury.io/js/perfect-scrollbar.svg" alt="npm"></a>	
<a href="https://twitter.com/intent/tweet/?text=Thanks+@mdbootstrap+for+maintaining+amazing+and+free+Perfect+Scrollbar+Plugin%20https://perfectscrollbar.com/&hashtags=javascript,code,webdesign,bootstrap"><img src="https://img.shields.io/twitter/url/http/shields.io.svg?style=social&label=Let%20us%20know%20you%20were%20here%21&"></a>
<a href="https://www.youtube.com/watch?v=c9B4TPnak1A"><img alt="YouTube Video Views" src="https://img.shields.io/youtube/views/c9B4TPnak1A?label=Bootstrap%205%20Tutorial%20Views&style=social"></a>
</p>

________



## Why perfect-scrollbar?

perfect-scrollbar is minimalistic but *perfect* (for us, and maybe for most
developers) scrollbar plugin.

* No change on design layout
* No manipulation on DOM tree
* Use plain `scrollTop` and `scrollLeft`
* Scrollbar style is fully customizable
* Efficient update on layout change

We hope you will love it!

## Live preview

Check out the [Live Preview](https://mdbootstrap.com/snippets/standard/marveluck/3498209?utm_source=GitHub&utm_medium=PerfectScrollbar) snippet. You can fork it right away for testing and experimenting purposes.



## Related resources


- [Scroll Back To Top](https://mdbootstrap.com/docs/standard/extended/back-to-top/?utm_source=GitHub&utm_medium=PerfectScrollbar)

- [Scrollspy](https://mdbootstrap.com/docs/standard/navigation/scrollspy/?utm_source=GitHub&utm_medium=PerfectScrollbar)

- [Scrollbar](https://mdbootstrap.com/docs/standard/methods/scrollbar/)

- [Scroll Status](https://mdbootstrap.com/docs/standard/plugins/scroll-status/?utm_source=GitHub&utm_medium=PerfectScrollbar)

## Social Media

 - [Twitter](https://twitter.com/MDBootstrap)

 - [Facebook](https://www.facebook.com/mdbootstrap) 

 - [Pinterest](https://pl.pinterest.com/mdbootstrap)

 - [Dribbble](https://dribbble.com/mdbootstrap)

 - [LinkedIn](https://www.linkedin.com/company/material-design-for-bootstrap)
 
 - [YouTube](https://www.youtube.com/channel/UC5CF7mLQZhvx8O5GODZAhdA)

## Get Free Material Design for Bootstrap 5

<table>
  <tbody>
    <tr>
      <td>
          <a href="https://mdbootstrap.com/docs/standard/" alt="Bootstrap 5" rel="dofollow">
          		<img src="https://mdbcdn.b-cdn.net/wp-content/themes/mdbootstrap4/content/en/_mdb5/standard/pro/_main/assets/mdb5-about-v2.jpg">
          </a>
      </td>
      <td>
        <ul style="list-style-type:none;">
        <li>Material Design 2.0 + latest Bootstrap 5 based on plain JavaScript. 700+ material UI components, super simple, 1 minute installation, free templates & much more</li>      
</ul>
      </td>
    </tr>
   </tbody>
</table>

## Free Tutorials

Huge collection of free and high-quality tutorials. Learn Bootstrap, Angular, React, Vue, WordPress and many more. Create your own websites and apps.


[Check it out](https://www.youtube.com/c/Mdbootstrap)
