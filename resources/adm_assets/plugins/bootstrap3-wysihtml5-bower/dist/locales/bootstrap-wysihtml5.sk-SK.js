/**
 * Slovak translation for bootstrap-wysihtml5
 */
(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define('bootstrap.wysihtml5.sk-SK', ['jquery', 'bootstrap.wysihtml5'], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
}(function($){
    $.fn.wysihtml5.locale["sk-SK"] = {
        font_styles: {
            normal: "Normálny text",
            h1: "Nadpis úrovne 1",
            h2: "Nadpis úrovne 2",
            h3: "Nadpis úrovne 3"
        },
        emphasis: {
            bold: "Tučné",
            italic: "<PERSON>rz<PERSON><PERSON>",
            underline: "Podčiarknuté"
        },
        lists: {
            unordered: "Neusporiadaný zoznam",
            ordered: "Číslovaný zoznam",
            outdent: "Zväčšiť odsadenie",
            indent: "<PERSON>menš<PERSON><PERSON> odsadenie"
        },
        link: {
            insert: "<PERSON><PERSON><PERSON><PERSON><PERSON> odkaz",
            cancel: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
        },
        image: {
            insert: "V<PERSON>ž<PERSON><PERSON> obrázok",
            cancel: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
        },
        html: {
            edit: "Editovať HTML"
        },
        colours: {
            black: "Čierna",
            silver: "Strieborná",
            gray: "Šedá",
            maroon: "Bordová",
            red: "Červená",
            purple: "Fialová",
            green: "Zelená",
            olive: "Olivová",
            navy: "Tmavomodrá",
            blue: "Modrá",
            orange: "Oranžová"
        }
    };
}));
