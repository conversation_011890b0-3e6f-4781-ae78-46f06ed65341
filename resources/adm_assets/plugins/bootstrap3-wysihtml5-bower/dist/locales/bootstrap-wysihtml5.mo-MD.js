/**
 * Moldavian translation for bootstrap-wysihtml5
 */
(function (factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define('bootstrap.wysihtml5.mo-MD', ['jquery', 'bootstrap.wysihtml5'], factory);
    } else {
        // Browser globals
        factory(jQuery);
    }
}(function($){
    $.fn.wysihtml5.locale["mo-MD"] = {
        font_styles: {
              normal: "Normal",
              h1: "Titlu 1",
              h2: "Titlu 2"
        },
        emphasis: {
              bold: "Bold",
              italic: "Cursiv",
              underline: "Accentuat"
        },
        lists: {
              unordered: "Neordonata",
              ordered: "Ordonata",
              outdent: "Margine",
              indent: "zimțuire"
        },
        link: {
              insert: "Indroduce link-ul",
              cancel: "Anula"
        },
        image: {
              insert: "Insera imagina",
              cancel: "Anula"
        },
        html: {
            edit: "Editare HTML"
        },

        colours: {
            black: "Negru",
            silver: "Argint",
            gray: "<PERSON><PERSON>",
            maroon: "<PERSON><PERSON><PERSON>",
            red: "<PERSON>o<PERSON><PERSON>",
            purple: "<PERSON>",
            green: "<PERSON>",
            olive: "Oliv",
            navy: "Marin",
            blue: "Albastru",
            orange: "Portocaliu"
        }
    };
}));
