<svg width="146" height="146" viewBox="0 0 146 146" fill="none" xmlns="http://www.w3.org/2000/svg">
<style>
<!--done-->
 @keyframes pulse {
        0% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
        70% {
          -webkit-transform: scale(1.2);
          transform: scale(1.2);
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      } 

    #path-1, #path-2, #path-3, #path-4, #path-5, #path-6, #path-7 {
      animation: pulse 2s ease-in-out infinite;
      -webkit-animation: pulse 2s ease-in-out infinite;
      -webkit-transform-origin: center; -ms-transform-origin: center; transform-origin: center; transform-box: fill-box;
    }
    #cercale{
        animation: circle 2s ease-in-out infinite;
    }
     @keyframes circle {
        0% {
            color: rgb(0 0 0 / 0%);
        }
        70% {
            color:#F00;
        }
        100% {
             color: rgb(0 0 0 / 0%);
        }
      } 
    #path-1, #path-3, #path-5, #path-7{
      animation-delay: 1s;
    }

    #path-2, #path-4, #path-6 ,cercale{
      animation-delay: 1.5s;
    }
</style>
<path id="path-1" d="M125.717 99.3384C120.408 116.81 108.611 131.169 92.5035 139.773L92.0916 139.002C124.922 121.473 137.369 80.5079 119.84 47.6772C111.349 31.7735 97.1729 20.1282 79.9277 14.8883C62.6824 9.64831 44.4198 11.4371 28.5202 19.9297L28.1083 19.1584C44.22 10.556 62.7137 8.74575 80.1856 14.0545C97.6574 19.3633 112.017 31.1605 120.62 47.2679C129.215 63.3729 131.026 81.8666 125.717 99.3384Z" fill="#FAB2B2"/>
<path id="path-2" d="M121.19 97.9624C116.45 113.563 105.773 127.355 90.2909 135.622L89.879 134.85C120.417 118.545 131.998 80.4335 115.694 49.8909C99.3887 19.3526 61.2729 7.77118 30.7346 24.0762L30.3227 23.3048C61.2877 6.77196 99.9322 18.514 116.469 49.4804C124.732 64.9616 125.929 82.3661 121.19 97.9624Z" fill="#FAB2B2"/>
<path id="path-3" d="M116.717 96.6032C112.139 111.671 101.964 124.053 88.0726 131.473L87.6607 130.702C101.349 123.394 111.367 111.193 115.876 96.3525C120.387 81.5073 118.846 65.7948 111.54 52.1122C104.232 38.4241 92.0317 28.4058 77.1907 23.8964C62.3455 19.3857 46.6329 20.9273 32.9503 28.2328L32.5385 27.4614C46.429 20.0449 62.3823 18.4803 77.4498 23.0585C92.5174 27.6368 104.899 37.811 112.32 51.7028C119.729 65.5866 121.295 81.5357 116.717 96.6032Z" fill="#FAB2B2"/>
<path id="path-4" d="M112.19 95.2277C108.151 108.52 99.0513 120.276 85.8556 127.321L85.4437 126.55C111.408 112.687 121.253 80.2856 107.39 54.3209C93.5272 28.3562 61.1263 18.5112 35.1616 32.3743L34.7497 31.6029C61.1411 17.512 94.075 27.5189 108.162 53.909C115.207 67.1047 116.228 81.935 112.19 95.2277Z" fill="#FAB2B2"/>
<path id="path-5" d="M107.691 93.8611C104.003 106 95.6935 116.739 83.6412 123.174L83.2293 122.403C106.907 109.761 115.884 80.2172 103.241 56.5435C90.5985 32.8657 61.055 23.8889 37.3813 36.5323L36.9694 35.7609C61.0685 22.8939 91.1492 32.0338 104.016 56.133C110.45 68.1756 111.38 81.7223 107.691 93.8611Z" fill="#FAB2B2"/>
<path id="path-6" d="M103.193 92.4945C99.8542 103.484 92.3318 113.202 81.4284 119.023L81.0165 118.252C102.402 106.834 110.512 80.1433 99.0937 58.7577C93.5613 48.3959 84.3257 40.8093 73.089 37.395C61.8523 33.9808 49.9572 35.1469 39.5954 40.6793L39.1835 39.9079C49.749 34.2632 61.8836 33.0782 73.3427 36.56C84.8018 40.0419 94.2246 47.7816 99.8651 58.3459C105.687 69.2493 106.532 81.5053 103.193 92.4945Z" fill="#FAB2B2"/>
<path id="path-7" d="M98.6954 91.1274C95.707 100.963 88.9758 109.661 79.2103 114.875L78.7984 114.103C97.8972 103.906 105.139 80.0732 94.9416 60.9745C84.7444 41.8757 60.9115 34.6341 41.8127 44.8313L41.4008 44.06C60.9263 33.6349 85.2921 41.0384 95.7171 60.5639C100.925 70.3184 101.684 81.2922 98.6954 91.1274Z" fill="#FAB2B2"/>
<circle id="cercale" cx="63" cy="75.9998" r="20" fill="#FAB2B2"/>
</svg>
