<svg width="112" height="109" viewBox="0 0 112 109" fill="none" xmlns="http://www.w3.org/2000/svg">

<style>
<!-- done -->
    svg{
        margin:10px;
    }
      @keyframes pulse {
        0% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
        70% {
          -webkit-transform: scale(1.5);
          transform: scale(1.5);
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      } 

      @-webkit-keyframes floating-top-left {
        from { -webkit-transform: translate(0,  0px); transform: translate(0,  0px); }
        65%  { -webkit-transform: translate(-10px, -10px); transform: translate(-10px, -10px); }
        to   { -webkit-transform: translate(0, 0px); transform: translate(0, 0px); }    
      } 

      @-webkit-keyframes floating-bottom-right {
        from { -webkit-transform: translate(0,  0px); transform: translate(0,  0px); }
        65%  { -webkit-transform: translate(10px, 10px); transform: translate(10px, 10px); }
        to   { -webkit-transform: translate(0, 0px); transform: translate(0, 0px); }    
      } 

      @-webkit-keyframes floating-top-top {
        from { -webkit-transform: translate(0,  0px); transform: translate(0,  0px);  } 
        65%  { -webkit-transform: translate(0, -10px); transform: translate(0, -10px); }
        to   { -webkit-transform: translate(0, 0px); transform: translate(0, 0px);  }    
      } 

      @-webkit-keyframes floating-top {
        from { -webkit-transform: translate(0,  0px); transform: translate(0,  0px); -webkit-transform: scale(1); transform: scale(1); }
        40%  {  -webkit-transform: scale(1.2); transform: scale(1.2) ;}
        65%  { -webkit-transform: translate(0, -10px); transform: translate(0, -10px); }
        to   { -webkit-transform: translate(0, 0px); transform: translate(0, 0px); -webkit-transform: scale(1); transform: scale(1); }    
      } 

      @-webkit-keyframes floating-down {
        from { -webkit-transform: translate(0,  0px); transform: translate(0,  0px); -webkit-transform: scale(1); transform: scale(1); }
        40%  {  -webkit-transform: scale(1.2); transform: scale(1.2) ;}
        65%  { -webkit-transform: translate(0, 10px); transform: translate(0, 10px); }
        to   { -webkit-transform: translate(0, 0px); transform: translate(0, 0px); -webkit-transform: scale(1); transform: scale(1); }    
      }

    #dot-31, #dot-32 , #dot-33 , #dot-34 , #dot-35 , #dot-36 {
      -weanimation: pulse 2s ease-in-out infinite;
      -webkit-animation: pulse 2s ease-in-out infinite;
      -webkit-transform-origin: center; -ms-transform-origin: center; transform-origin: center; transform-box: fill-box;
    } 

    #dot-1, #dot-2, #dot-3, #dot-4, #dot-5, #dot-6, #dot-7, #dot-8, #dot-9, #dot-10, #dot-11, #dot-12, #dot-13, #dot-14, #dot-15, #dot-16, #dot-17, #dot-18, #dot-19, #dot-20, #dot-21, #dot-22, #dot-23, #dot-24, #dot-25, #dot-26, #dot-27, #dot-28, #dot-29, #dot-30 {
      animation: floating-top 2s ease-in-out infinite;
      -webkit-animation: floating-top 2s ease-in-out infinite;
    }

   #dot-1, #dot-3, #dot-5, #dot-7,#dot-9,#dot-11, #dot-13,#dot-15,#dot-17, #dot-19, #dot-21, #dot-23, #dot-25, #dot-27, #dot-29 {
      animation-delay: 1s; 
    }

    #message-1, #message-2 {
      animation-delay: 1.5s;
      animation: floating-top-top 2s ease-in-out infinite;
      -webkit-animation: floating-top-top 2s ease-in-out infinite;
    }

    #dot-32, #dot-35 {
      animation-delay: 1.5s;
    }

    #dot-33, #dot-36 {
      animation-delay: 2s;
    }

    #grid-1, #grid-2 {
      animation-delay: 1.2s;
      animation: floating-top-left 1.5s ease-in-out infinite;
      -webkit-animation: floating-top-left 1.5s ease-in-out infinite;
    }

    #grid-2 {
      animation: floating-bottom-right 1.5s ease-in-out infinite;
      -webkit-animation: floating-bottom-right 1.5s ease-in-out infinite;
    }

</style>






<g id="grid-1">
    <path id="path-1" d="M1.76762 3.23936C0.878058 3.23936 0.147949 2.51764 0.147949 1.61968C0.147949 0.730115 0.869666 0 1.76762 0C2.66558 0 3.3873 0.721723 3.3873 1.61968C3.3873 2.50924 2.65719 3.23936 1.76762 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M16.991 3.23936C16.1014 3.23936 15.3713 2.51764 15.3713 1.61968C15.3713 0.730115 16.0931 0 16.991 0C17.8806 0 18.6107 0.721723 18.6107 1.61968C18.6023 2.50924 17.8806 3.23936 16.991 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 3.23936C31.3163 3.23936 30.5862 2.51764 30.5862 1.61968C30.5862 0.730115 31.3079 0 32.2059 0C33.0954 0 33.8255 0.721723 33.8255 1.61968C33.8255 2.50924 33.1038 3.23936 32.2059 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 3.23936C46.5397 3.23936 45.8096 2.51764 45.8096 1.61968C45.8096 0.730115 46.5313 0 47.4293 0C48.3189 0 49.049 0.721723 49.049 1.61968C49.049 2.50924 48.3189 3.23936 47.4293 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 3.23936C61.7546 3.23936 61.0244 2.51764 61.0244 1.61968C61.0244 0.730115 61.7462 0 62.6441 0C63.5337 0 64.2638 0.721723 64.2638 1.61968C64.2638 2.50924 63.5421 3.23936 62.6441 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 3.23936C76.9779 3.23936 76.2478 2.51764 76.2478 1.61968C76.2478 0.730115 76.9695 0 77.8675 0C78.7655 0 79.4873 0.721723 79.4873 1.61968C79.4873 2.50924 78.7571 3.23936 77.8675 3.23936Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M1.76762 17.1199C0.878058 17.1199 0.147949 16.3982 0.147949 15.5002C0.147949 14.6107 0.869666 13.8806 1.76762 13.8806C2.66558 13.8806 3.3873 14.6023 3.3873 15.5002C3.3873 16.3898 2.65719 17.1199 1.76762 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M16.991 17.1199C16.1014 17.1199 15.3713 16.3982 15.3713 15.5002C15.3713 14.6107 16.0931 13.8806 16.991 13.8806C17.8806 13.8806 18.6107 14.6023 18.6107 15.5002C18.6023 16.3898 17.8806 17.1199 16.991 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 17.1199C31.3163 17.1199 30.5862 16.3982 30.5862 15.5002C30.5862 14.6107 31.3079 13.8806 32.2059 13.8806C33.0954 13.8806 33.8255 14.6023 33.8255 15.5002C33.8255 16.3898 33.1038 17.1199 32.2059 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 17.1199C46.5397 17.1199 45.8096 16.3982 45.8096 15.5002C45.8096 14.6107 46.5313 13.8806 47.4293 13.8806C48.3189 13.8806 49.049 14.6023 49.049 15.5002C49.049 16.3898 48.3189 17.1199 47.4293 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 17.1199C61.7546 17.1199 61.0244 16.3982 61.0244 15.5002C61.0244 14.6107 61.7462 13.8806 62.6441 13.8806C63.5337 13.8806 64.2638 14.6023 64.2638 15.5002C64.2638 16.3898 63.5421 17.1199 62.6441 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 17.1199C76.9779 17.1199 76.2478 16.3982 76.2478 15.5002C76.2478 14.6107 76.9695 13.8806 77.8675 13.8806C78.7655 13.8806 79.4873 14.6023 79.4873 15.5002C79.4873 16.3898 78.7571 17.1199 77.8675 17.1199Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M1.76762 30.9921C0.878058 30.9921 0.147949 30.2704 0.147949 29.3724C0.147949 28.4829 0.869666 27.7528 1.76762 27.7528C2.66558 27.7528 3.3873 28.4745 3.3873 29.3724C3.3873 30.2704 2.65719 30.9921 1.76762 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M16.991 30.9921C16.1014 30.9921 15.3713 30.2704 15.3713 29.3724C15.3713 28.4829 16.0931 27.7528 16.991 27.7528C17.8806 27.7528 18.6107 28.4745 18.6107 29.3724C18.6023 30.2704 17.8806 30.9921 16.991 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 30.9921C31.3163 30.9921 30.5862 30.2704 30.5862 29.3724C30.5862 28.4829 31.3079 27.7528 32.2059 27.7528C33.0954 27.7528 33.8255 28.4745 33.8255 29.3724C33.8255 30.2704 33.1038 30.9921 32.2059 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 30.9921C46.5397 30.9921 45.8096 30.2704 45.8096 29.3724C45.8096 28.4829 46.5313 27.7528 47.4293 27.7528C48.3189 27.7528 49.049 28.4745 49.049 29.3724C49.049 30.2704 48.3189 30.9921 47.4293 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 30.9921C61.7546 30.9921 61.0244 30.2704 61.0244 29.3724C61.0244 28.4829 61.7462 27.7528 62.6441 27.7528C63.5337 27.7528 64.2638 28.4745 64.2638 29.3724C64.2638 30.2704 63.5421 30.9921 62.6441 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 30.9921C76.9779 30.9921 76.2478 30.2704 76.2478 29.3724C76.2478 28.4829 76.9695 27.7528 77.8675 27.7528C78.7655 27.7528 79.4873 28.4745 79.4873 29.3724C79.4873 30.2704 78.7571 30.9921 77.8675 30.9921Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M1.76762 44.8727C0.878058 44.8727 0.147949 44.151 0.147949 43.253C0.147949 42.3634 0.869666 41.6333 1.76762 41.6333C2.66558 41.6333 3.3873 42.355 3.3873 43.253C3.3873 44.1426 2.65719 44.8727 1.76762 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1" d="M16.991 44.8727C16.1014 44.8727 15.3713 44.151 15.3713 43.253C15.3713 42.3634 16.0931 41.6333 16.991 41.6333C17.8806 41.6333 18.6107 42.355 18.6107 43.253C18.6023 44.1426 17.8806 44.8727 16.991 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 44.8727C31.3163 44.8727 30.5862 44.151 30.5862 43.253C30.5862 42.3634 31.3079 41.6333 32.2059 41.6333C33.0954 41.6333 33.8255 42.355 33.8255 43.253C33.8255 44.1426 33.1038 44.8727 32.2059 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 44.8727C46.5397 44.8727 45.8096 44.151 45.8096 43.253C45.8096 42.3634 46.5313 41.6333 47.4293 41.6333C48.3189 41.6333 49.049 42.355 49.049 43.253C49.049 44.1426 48.3189 44.8727 47.4293 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 44.8727C61.7546 44.8727 61.0244 44.151 61.0244 43.253C61.0244 42.3634 61.7462 41.6333 62.6441 41.6333C63.5337 41.6333 64.2638 42.355 64.2638 43.253C64.2638 44.1426 63.5421 44.8727 62.6441 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 44.8727C76.9779 44.8727 76.2478 44.151 76.2478 43.253C76.2478 42.3634 76.9695 41.6333 77.8675 41.6333C78.7655 41.6333 79.4873 42.355 79.4873 43.253C79.4873 44.1426 78.7571 44.8727 77.8675 44.8727Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M1.76762 58.7532C0.878058 58.7532 0.147949 58.0315 0.147949 57.1336C0.147949 56.244 0.869666 55.5139 1.76762 55.5139C2.66558 55.5139 3.3873 56.2356 3.3873 57.1336C3.3873 58.0231 2.65719 58.7532 1.76762 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M16.991 58.7532C16.1014 58.7532 15.3713 58.0315 15.3713 57.1336C15.3713 56.244 16.0931 55.5139 16.991 55.5139C17.8806 55.5139 18.6107 56.2356 18.6107 57.1336C18.6023 58.0231 17.8806 58.7532 16.991 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 58.7532C31.3163 58.7532 30.5862 58.0315 30.5862 57.1336C30.5862 56.244 31.3079 55.5139 32.2059 55.5139C33.0954 55.5139 33.8255 56.2356 33.8255 57.1336C33.8255 58.0231 33.1038 58.7532 32.2059 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 58.7532C46.5397 58.7532 45.8096 58.0315 45.8096 57.1336C45.8096 56.244 46.5313 55.5139 47.4293 55.5139C48.3189 55.5139 49.049 56.2356 49.049 57.1336C49.049 58.0231 48.3189 58.7532 47.4293 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 58.7532C61.7546 58.7532 61.0244 58.0315 61.0244 57.1336C61.0244 56.244 61.7462 55.5139 62.6441 55.5139C63.5337 55.5139 64.2638 56.2356 64.2638 57.1336C64.2638 58.0231 63.5421 58.7532 62.6441 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 58.7532C76.9779 58.7532 76.2478 58.0315 76.2478 57.1336C76.2478 56.244 76.9695 55.5139 77.8675 55.5139C78.7655 55.5139 79.4873 56.2356 79.4873 57.1336C79.4873 58.0231 78.7571 58.7532 77.8675 58.7532Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M1.76762 72.6254C0.878058 72.6254 0.147949 71.9037 0.147949 71.0058C0.147949 70.1162 0.869666 69.3861 1.76762 69.3861C2.66558 69.3861 3.3873 70.1078 3.3873 71.0058C3.3873 71.9037 2.65719 72.6254 1.76762 72.6254Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M16.991 72.6254C16.1014 72.6254 15.3713 71.9037 15.3713 71.0058C15.3713 70.1162 16.0931 69.3861 16.991 69.3861C17.8806 69.3861 18.6107 70.1078 18.6107 71.0058C18.6023 71.9037 17.8806 72.6254 16.991 72.6254Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M32.2059 72.6254C31.3163 72.6254 30.5862 71.9037 30.5862 71.0058C30.5862 70.1162 31.3079 69.3861 32.2059 69.3861C33.0954 69.3861 33.8255 70.1078 33.8255 71.0058C33.8255 71.9037 33.1038 72.6254 32.2059 72.6254Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M47.4293 72.6254C46.5397 72.6254 45.8096 71.9037 45.8096 71.0058C45.8096 70.1162 46.5313 69.3861 47.4293 69.3861C48.3189 69.3861 49.049 70.1078 49.049 71.0058C49.049 71.9037 48.3189 72.6254 47.4293 72.6254Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M62.6441 72.6254C61.7546 72.6254 61.0244 71.9037 61.0244 71.0058C61.0244 70.1162 61.7462 69.3861 62.6441 69.3861C63.5337 69.3861 64.2638 70.1078 64.2638 71.0058C64.2638 71.9037 63.5421 72.6254 62.6441 72.6254Z" fill="#4E5AFF"/>
    <path id="path-1"  d="M77.8675 72.6254C76.9779 72.6254 76.2478 71.9037 76.2478 71.0058C76.2478 70.1162 76.9695 69.3861 77.8675 69.3861C78.7655 69.3861 79.4873 70.1078 79.4873 71.0058C79.4873 71.9037 78.7571 72.6254 77.8675 72.6254Z" fill="#4E5AFF"/>

</g>
<g>
    <path d="M112 39L45 108.5L45 39L112 39Z" fill="#4E5AFF"/>
</g>
</svg>
