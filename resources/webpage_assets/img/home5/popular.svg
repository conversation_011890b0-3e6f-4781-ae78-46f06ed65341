<svg width="113" height="198" viewBox="0 0 113 198" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_119_648)">
<ellipse cx="56.5" cy="95" rx="52.5" ry="95" fill="#FF5E14"/>
</g>
<defs>
<filter id="filter0_d_119_648" x="0" y="0" width="113" height="198" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_119_648"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_119_648" result="shape"/>
</filter>
</defs>
</svg>
