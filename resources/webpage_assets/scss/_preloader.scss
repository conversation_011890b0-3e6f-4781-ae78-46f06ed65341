.preloader-close {
    position: fixed;
    z-index: 99999999;
    font-size: 26px;
    background: $white;
    width: 40px;
    height: 40px;
    line-height: 36px;
    text-align: center;
    border-radius: 50%;
    cursor: pointer;
    right: 30px;
    top: 30px;
}

#preloader {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: #121212;
    z-index: 999;
}

.sk-three-bounce {
    margin: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background-color: #121212;
}

.sk-three-bounce .sk-child {
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-color: $second-color;
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
    animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
}

.sk-three-bounce.is-brick-red.sk-three-bounce .sk-child {
    background-color: $second-color;
}

.sk-three-bounce.is-mainly-blue.sk-three-bounce .sk-child {
    background-color: $second-color;
}

.sk-three-bounce.is-orange.sk-three-bounce .sk-child {
    background-color: $second-color;
}

.sk-three-bounce.is-purple.sk-three-bounce .sk-child {
    background-color: $second-color;
}

.sk-three-bounce .sk-bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.sk-three-bounce .sk-bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-three-bounce {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes sk-three-bounce {
    0%,
    80%,
    100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}