.element-7 {
    background: #FF5E14;
    padding: 19.5px 0;

    &__slide {
        &__item {
            text-align: center;

            span {
                font-size: 20px;
                font-weight: 600;
                text-transform: uppercase;
                color: #fff;
                letter-spacing: -0.02em;
                font-family: $text-font2;
                position: relative;
                padding-right: 37px;
                margin-right: 32px;

                @media #{$xs} {
                    padding-right: 25px;
                    margin-right: 15px;
                }

                &::after {
                    content: "";
                    width: 6px;
                    height: 6px;
                    background: #fff;
                    position: absolute;
                    border-radius: 30px;
                    top: 11px;
                    right: 0;
                }
            }
        }
    }
}

// .swiper-wrapper {
//     transition-timing-function: linear !important;
// }