.footer-7 {
    background: #141820;

    &__top {
        &__wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 53px;
            padding-bottom: 55px;
            border-bottom: 1px solid rgb(255 255 255 / 10%);

            @media #{$md,$sm,$xs} {
                display: block;
            }
        }
    }

    &__btn {
        &__wrapper {
            display: flex;
            align-items: center;
            gap: 10px;

            @media #{$md,$sm,$xs} {
                margin-top: 30px;
            }

            @media #{$xs} {
                gap: 5px;
            }
        }
    }

    &__widget {
        padding-top: 105px;
        padding-bottom: 55px;

        @media #{$lg} {

            &.widget-1,
            &.widget-2,
            &.widget-3 {
                padding-bottom: 30px;
            }
        }

        @media #{$lg,$md,$sm,$xs} {
            &.widget-4 {
                padding-top: 0px;
            }
        }

        &__dec {
            font-size: 16px;
            font-weight: 400;
            color: #fff;
            line-height: 28px;
            padding-right: 19px;
            margin-top: 35px;
            margin-bottom: 38px;
            font-family: $text-font2;
        }

        &__social {
            display: flex;
            align-items: center;
            gap: 15px;

            a {
                color: #fff;
                width: 40px;
                height: 40px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border: 1px solid rgb(255 255 255 / 10%);
                border-radius: 30px;

                &:hover {
                    background: #FF5E14;
                    border-color: #FF5E14;
                }
            }
        }

        &__title {
            font-size: 24px;
            color: #fff;
            font-weight: 600;
            margin-bottom: 43px;
            line-height: 1;
            text-transform: none;
            font-family: $text-font2;
        }

        &__menu {
            ul {
                li {
                    a {
                        font-size: 16px;
                        color: #fff;
                        font-weight: 400;
                        line-height: 28px;
                        margin-bottom: 15px;
                        display: inline-block;
                        font-family: $text-font2;

                        &:hover {
                            color: #FF5E14;
                        }
                    }
                }
            }
        }

        &__contact {
            div {
                display: flex;
                align-items: start;
                gap: 20px;
                margin-bottom: 10px;

                a {
                    font-size: 16px;
                    color: #fff;
                    display: inline-block;
                    font-weight: 400;
                    line-height: 28px;
                    font-family: $text-font2;

                    @media #{$sm} {
                        display: block;
                    }

                    &:hover {
                        color: #FF5E14;
                    }
                }
            }
        }

        &.widget {
            &-1 {
                @media #{$md,$sm,$xs} {
                    padding-top: 70px;
                }
            }

            &-2 {
                margin-left: 60px;

                @media #{$xl} {
                    margin-left: 25px;
                }

                @media #{$sm,$xs} {
                    margin-left: 0px;
                    padding-top: 0px;
                }
            }

            &-3 {
                margin-left: 5px;

                @media #{$xl,$lg,$md,$sm,$xs} {
                    margin-left: 0px;
                }

                @media #{$md,$sm,$xs} {
                    margin-left: 0px;
                    padding-top: 0px;
                }
            }

            &-4 {
                margin-left: -40px;

                @media #{$lg,$md,$sm,$xs} {
                    margin-left: 0px;
                }
            }
        }
    }

    &__copyright {
        &__text {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 25px;
            padding-bottom: 20px;
            border-top: 1px solid rgb(255 255 255 / 10%);

            @media #{$sm,$xs} {
                display: block;
                text-align: center;
            }
        }

        &__dec {
            font-size: 16px;
            color: rgb(255 255 255 / 70%);
            font-weight: 400;
            font-family: $text-font2;

            @media #{$xs} {
                margin-bottom: 20px;
            }

            a {
                color: rgb(255, 255, 255, 1);
                font-weight: 400;

                &:hover {
                    color: #FF5E14;
                }
            }
        }

        &__menu {
            a {
                font-size: 16px;
                color: rgb(255 255 255 / 70%);
                font-weight: 400;
                font-family: $text-font2;
                padding-left: 15px;
                margin-left: 15px;
                position: relative;

                &:hover {
                    color: #FF5E14;
                }

                &::before {
                    content: "";
                    width: 1px;
                    height: 15PX;
                    left: 0;
                    top: 5px;
                    position: absolute;
                    background-color: rgba(255, 255, 255, 0.1);
                }

                &:first-of-type {
                    padding-left: 0;
                    margin-left: 0;

                    &::before {
                        display: none;
                    }
                }
            }
        }
    }

}