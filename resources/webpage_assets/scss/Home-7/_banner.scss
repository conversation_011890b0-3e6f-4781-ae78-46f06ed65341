.banner-7 {
    &__top {
        &__wrapper {
            background: $heading2;
            padding: 92px 0 80px 0;
            position: relative;
            overflow: hidden;

            @media #{$xs} {
                padding: 70px 0 70px 0;
            }
        }

        &__shape-1 {
            position: absolute;
            width: 900px;
            height: 900px;
            right: -22px;
            top: 316px;
            border-radius: 500px;
            background: rgba(255, 94, 20, 0.4);
            filter: blur(200px);
        }
    }

    &__title {
        font-size: 80px;
        color: #fff;
        font-weight: 700;
        line-height: 96px;
        margin: 0;
        font-family: $text-font2;
        text-transform: capitalize;

        @media #{$sm} {
            font-size: 55px;
            line-height: 70px;
        }

        @media #{$xs} {
            font-size: 32px;
            line-height: 48px;
        }
    }

    &__dec {
        font-size: 18px;
        color: #B4B7BE;
        font-weight: 400;
        margin-top: 18px;
        margin-bottom: 45px;
        font-family: $text-font2;
    }

    &__btn {
        &__wrapper {
            display: flex;
            align-items: center;
            gap: 20px;

            @media #{$xs} {
                display: block;
            }
        }
    }

    &__call {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.02em;
        display: flex;
        align-items: center;
        gap: 10px;

        span {
            width: 50px;
            height: 50px;
            background: #FF5E14;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 500px;
        }

        &:hover {
            color: #FF5E14;
        }
    }

    &__bottom {
        &__thumb {
            img {
                @media #{$xxl} {
                    max-width: inherit;
                }
            }
        }
    }

    &__play {
        &__btn {
            width: 220px;
            height: 220px;
            background: rgb(255 255 255 / 10%);
            border-radius: 500px;
            border: 2px solid rgb(255 255 255 / 30%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            bottom: 47%;
            right: 315px;
            z-index: 9;
            animation-name: playVideo;
            animation: playVideo infinite 2s;

            @media #{$xl} {
                bottom: 35%;
                right: 200px;
            }

            @media #{$lg} {
                bottom: 30%;
                right: 120px;
            }

            @media #{$md} {
                width: 190px;
                height: 190px;
                bottom: 22%;
                right: 50px;
            }

            @media #{$sm} {
                width: 150px;
                height: 150px;
                bottom: 13%;
                right: 50px;
            }

            @media #{$xs} {
                width: 110px;
                height: 110px;
                bottom: 11%;
                right: 22px;
            }

            a {
                width: 126px;
                height: 126px;
                background: #fff;
                display: inline-flex;
                border-radius: 500px;
                align-items: center;
                justify-content: center;
                border: 2px solid rgb(255 255 255 / 30%);
                animation-name: playVideo;
                animation: playVideo infinite 2s;

                @media #{$sm} {
                    width: 110px;
                    height: 110px;
                }

                @media #{$xs} {
                    width: 80px;
                    height: 80px;
                }
            }
        }
    }
}