.services-7 {
    &__top {
        &__dec {
            font-size: 18px;
            color: #53545A;
            font-weight: 400;
            line-height: 28px;
            margin-bottom: 26px;
        }

        &__content {
            padding-left: 40px;
            margin-top: 50px;

            @media #{$xxl} {
                padding-left: 0px;
            }

            @media #{$md,$sm,$xs} {
                padding-left: 0px;
                margin-top: 20px;
            }
        }
    }

    &__item {
        display: flex;
        align-items: start;
        gap: 20px;
        padding: 50px 30px;
        position: relative;
        transition: .3s;
        margin-bottom: -1px;
        border-top: 1px solid rgb(20 24 32 / 30%);
        border-bottom: 1px solid rgb(20 24 32 / 30%);

        &:hover {
            border: none;
            padding-top: 30px;
            padding-bottom: 28px;

            & .services-7 {
                &__icon {
                    background: #fff
                }

                &__title {
                    color: #fff;
                }

                &__dec {
                    display: block;

                    @media #{$xs} {
                        display: none;
                    }
                }

                &__btn {
                    &__wrapper {
                        a {
                            display: block;
                        }
                    }
                }

                &__bg {
                    &__thumb {
                        display: block;
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }

            @media #{$xs} {
                padding-top: 20px;
                padding-bottom: 40px;
            }
        }

        @media #{$xs} {
            display: block;
        }
    }

    &__icon {
        width: 60px;
        height: 60px;
        background: rgb(255 94 20 / 8%);
        text-align: center;
        line-height: 60px;
        border-radius: 4px;
        transition: .3s;

        @media #{$xs} {
            margin-bottom: 20px;
        }
    }

    &__title {
        font-size: 36px;
        color: #141820;
        font-weight: 600;
        margin-bottom: 0;
        text-transform: none;
        transition: .3s;
        font-family: $text-font2;

        @media #{$xs} {
            font-size: 27px;
        }
    }

    &__dec {
        font-size: 16px;
        line-height: 26px;
        font-weight: 400;
        color: rgb(255 255 255 / 80%);
        margin-top: 5px;
        margin-bottom: 15px;
        font-family: $text-font2;
        display: none;
        transition: .3s;

        @media #{$sm,$xs} {
            br {
                display: none;
            }
        }
    }

    &__btn {
        &__wrapper {
            a {
                font-size: 16px;
                font-weight: 600;
                color: #fff;
                font-family: $text-font2;
                display: none;
                transition: .3s;

                i {
                    margin-left: 3px;
                }

                &:hover {
                    color: #FF5E14;
                }
            }
        }
    }

    &__bg {
        &__thumb {
            position: absolute;
            z-index: -1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            visibility: hidden;
            transition: .9s;

            img {
                max-width: inherit;

                @media #{$xxl} {
                    max-width: 100%;
                }
            }
        }
    }
}