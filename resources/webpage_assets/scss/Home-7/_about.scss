.about-7 {
    position: relative;

    @media #{$md,$sm,$xs} {
        padding-top: 650px;
    }

    @media #{$xs} {
        padding-top: 500px;
    }

    &__dec {
        font-size: 18px;
        color: #53545A;
        font-weight: 400;
        line-height: 28px;
        font-family: $text-font2;
    }

    &__content {
        padding-top: 70px;
        padding-bottom: 70px;
        padding-left: 40px;

        &__wrapper {
            background: #FFF7F3;
            padding: 52px 0 45px 0;
            border-radius: 0 10px 10px 0;
            margin-left: -30px;
        }

        @media #{$lg} {
            padding-left: 10px;
        }

        @media #{$sm,$xs} {
            padding-left: 0px;
            padding-bottom: 40px;
        }
    }

    &__icon {
        width: 60px;
        height: 60px;
        background: #fff;
        border-radius: 4px;
        text-align: center;
        line-height: 60px;
    }

    &__title {
        font-size: 24px;
        font-weight: 600;
        color: #141820;
        margin-top: 22px;
        margin-bottom: 10px;
        text-transform: capitalize;
        font-family: $text-font2;

        @media #{$lg} {
            font-size: 20px;
        }
    }

    &__item {
        p {
            font-size: 16px;
            color: #53545A;
            font-weight: 400;
            line-height: 26px;
            font-family: $text-font2;
        }

        &.item-1 {
            margin-left: 30px;
            position: relative;

            &::after {
                content: "";
                width: 1px;
                height: 220px;
                background-color: rgba(20, 24, 32, 0.1);
                position: absolute;
                right: 0;
                top: 0;
            }

            @media #{$xs} {
                margin-bottom: 30px;
            }
        }

        &.item-2 {
            margin-left: 5px;

            p {
                padding-right: 10px;
            }

            @media #{$xs} {
                margin-left: 30px;
            }
        }

    }

    &__bg {
        &__thumb {
            position: absolute;
            width: 49.1%;
            height: 100%;
            border-radius: 0 10px 0px 0;
            background-position: center;
            background-size: cover;

            @media #{$md,$sm,$xs} {
                width: 100%;
                height: 670px;
                top: 0;
                left: 0;
            }

            @media #{$xs} {
                width: 100%;
                height: 500px;
                top: 0;
                left: 0;
            }
        }
    }

    &__number {
        font-size: 50px;
        font-weight: 700;
        color: #fff;
        line-height: 40px;
        font-family: $text-font2;
    }

    &__rating {
        width: 210px;
        height: 210px;
        background: #FF5E14;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0;
        right: 0px;

        span {
            font-size: 18px;
            color: #fff;
            font-weight: 400;
            line-height: 40px;
            font-family: $text-font2;
        }
    }
}