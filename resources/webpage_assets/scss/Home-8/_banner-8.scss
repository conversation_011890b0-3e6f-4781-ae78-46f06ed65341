.banner-8 {
    position: relative;

    &__section-space {
        padding-top: 332px;
        padding-bottom: 218px;

        @media #{$md} {
            padding-top: 240px;
            padding-bottom: 190px;
        }

        @media #{$sm} {
            padding-top: 200px;
            padding-bottom: 150px;
        }

        @media #{$xs} {
            padding-top: 120px;
            padding-bottom: 140px;
        }
    }

    &::before {
        content: "";
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        position: absolute;
        background: linear-gradient(90deg, #141820 15.93%, rgba(20, 24, 32, 0) 100%);
    }

    &__subtitle {
        font-size: 16px;
        font-weight: 500;
        color: #FFFFFF;
        font-family: $text-font2;
    }

    &__title {
        font-size: 80px;
        color: #fff;
        font-weight: 700;
        line-height: 96px;
        text-transform: none;
        margin-top: 2px;
        margin-bottom: 35px;
        font-family: $text-font2;

        @media #{$md,$sm,$xs} {
            br {
                display: none;
            }
        }

        @media #{$md} {
            font-size: 66px;
            line-height: 80px;
        }

        @media #{$sm} {
            font-size: 47px;
            line-height: 65px;
        }

        @media #{$xs} {
            font-size: 32px;
            line-height: 45px;
        }
    }

    &__wrapper {
        position: relative;
        z-index: 1;
    }

    &__bg {
        &__shape {
            &__01 {
                width: 130.53px;
                height: 1134.08px;
                background: rgba(255, 255, 255, 0.1);
                transform: rotate(30deg);
                position: absolute;
                right: 188px;
                top: -60px;
            }
        }

        &__thumb {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: all 4s ease;
        }

        &__text {
            span {
                font-weight: 800;
                font-size: 160px;
                line-height: 96px;
                text-transform: uppercase;
                color: #FFFFFF;
                transform: rotate(-90deg);
                position: absolute;
                bottom: 375px;
                left: -65px;
                opacity: 0.02;
                font-family: $text-font2;
            }
        }
    }

    &__shape {
        &__01 {
            position: absolute;
            left: 30.5%;
            bottom: 23%;

            @media #{$sm} {
                left: 35.5%;
            }

            @media #{$xs} {
                left: 65.5%;
            }
        }
    }

    &__text__shape {
        a {
            font-size: 16px;
            color: #fff;
            font-weight: 500;
            text-transform: uppercase;
            position: absolute;
            bottom: 95px;
            left: 50%;
            transform: translateX(-50%) rotate(-90deg);
            font-family: $text-font2;
            display: flex;
            align-items: center;
            line-height: 1;
            gap: 10px;

            @media #{$xs} {
                font-size: 14px;
                bottom: 60px;
            }

            i {
                color: #FF5E14;
                font-size: 28px;
                display: inline-block;
            }
        }
    }

    &__button {
        width: 60px;
        height: 60px;
        background: rgb(255 255 255 / 10%);
        position: absolute;
        z-index: 9999;
        bottom: 390px;
        left: 60px;
        font-size: 21px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        @media #{$xl} {
            bottom: 60px;
            right: 10%;
            left: auto;
        }

        @media #{$lg} {
            bottom: 60px;
            left: auto;
            right: 12%;
        }

        &__next {
            left: auto;
            right: 60px;

            @media #{$xl,$lg} {
                right: 5%;
            }
        }
    }

}

.swiper-slide {
    &.swiper-slide-active {
        & .banner-8 {
            &__wrapper {
                animation: fade-anim-left 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
                opacity: 0;
            }
        }

        & .banner-8 {
            &__bg {
                &__thumb {
                    transform: scale(1.1);
                }
            }
        }
    }
}