.faq-8 {
    &__tab {
        &__wrapper {
            margin-right: 78px;

            @media #{$md,$sm,$xs} {
                margin-right: 0px;
            }

            & .accordion {
                &-buttons {
                    font-size: 20px;
                    color: #141820;
                    font-weight: 600;
                    line-height: 1;
                    background: none;
                    display: inline-block;
                    width: 100%;
                    text-align: start;
                    padding: 25px 30px;
                    padding-top: 18px;
                    transition: .3s;
                    position: relative;
                    font-family: $text-font2;

                    @media #{$xs} {
                        font-size: 19px;
                        line-height: 1.3;
                    }

                    &.collapsed {
                        padding-top: 25px;

                        &::before {
                            content: "\f055";
                        }
                    }

                    &::before {
                        content: "\f056";
                        font-family: "Font Awesome 5 Pro";
                        font-weight: 400;
                        right: 30px;
                        position: absolute;
                        color: #FF5E14;
                        transition: .3s;

                        @media #{$xs} {
                            display: none;
                        }
                    }
                }

                &-item {
                    color: inherit;
                    background-color: rgb(255 94 20 / 4%);
                    border: none;
                    margin-bottom: 20px;
                }

                &-body {
                    p {
                        font-size: 16px;
                        color: #53545A;
                        font-weight: 400;
                        line-height: 26px;
                        margin-top: -3px;
                        font-family: $text-font2;
                    }
                }

                &-body {
                    padding: 0 30px;
                    padding-bottom: 28px;
                }
            }
        }
    }

    &__contact {
        &__form {
            background: #141820;
            padding: 50px 30px;
            padding-top: 47px;
            margin-left: -68px;

            @media #{$md,$sm,$xs} {
                margin-left: 0px;
            }

            &__input {
                input {
                    border: 1px solid rgb(255 255 255 / 10%);
                    background: rgb(255 255 255 / 10%);
                    color: #fff;
                    margin-bottom: 30px;
                    padding: 10px 14px;
                    width: 100%;

                    &::placeholder {
                        font-size: 14px;
                        color: #fff;
                        font-weight: 400;
                        font-family: $text-font2;
                    }
                }
            }

            &__title {
                font-size: 24px;
                color: #fff;
                text-align: center;
                text-transform: none;
                font-weight: 600;
                line-height: 1;
                margin-bottom: 48px;
            }

            &__textarea {
                textarea {
                    background: rgb(255 255 255 / 10%);
                    border: 1px solid rgb(255 255 255 / 10%);
                    width: 100%;
                    height: 130px;
                    color: #fff;
                    padding: 14px;
                    margin-bottom: 21px;

                    &:focus {
                        border: 1px solid rgb(255 255 255 / 10%);
                    }

                    &::placeholder {
                        font-size: 14px;
                        color: #fff;
                        font-weight: 400;
                        font-family: $text-font2;
                    }
                }
            }
        }
    }
}