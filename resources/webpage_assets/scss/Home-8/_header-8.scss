  .header-8 {
      position: absolute;
      z-index: 2;

      &.sticky {
          background: #141820;

          @media #{$lg} {
              padding-top: 18px;
          }
      }

      & .header-top-bar {
          background-color: transparent;
          padding: 5.5px 0px;

          @media (max-width: 610px) {
              display: none !important;
          }

          @media (max-width: 991px) {
              padding: 0;
          }

          .top-left-content {
              li {
                  display: inline-block;
                  border-right: 1px solid rgb(255 255 255 / 10%);
                  padding-right: 20px;
                  margin-right: 30px;

                  @media (max-width: 690px) {
                      padding-right: 15px;
                      margin-right: 15px;
                  }

                  &:last-of-type {
                      border: 0px;
                      margin-right: 0;
                      padding-right: 0;
                  }

                  a {
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      line-height: 1;
                      color: #fff;
                      font-size: 14px;
                      font-weight: 500;
                      font-family: $text-font2;

                      &:hover {
                          color: #FF5E14;
                      }

                      i {
                          margin-right: 0;
                          color: $primary2;
                      }
                  }

              }
          }

          & .top-social-icons {
              a {
                  font-weight: 300;
                  margin-left: 16px;
                  color: #fff;
                  font-size: 15px;
                  display: inline-block;

                  &:hover {
                      color: $second-color !important
                  }

                  &:first-child {
                      margin-left: 0px !important;
                  }
              }

              span {
                  color: #fff;
                  font-size: 14px;
                  font-weight: 500;
                  font-family: $text-font2;
              }
          }
      }

      & .main_wrapper {
          border-top: 1px solid rgb(255 255 255 / 10%);
          border-bottom: 1px solid rgb(255 255 255 / 10%);

          @media #{$lg,$md,$sm,$xs} {
              padding: 15px;
          }

          & .logo {
              position: relative;
              margin-top: 0;
              margin-bottom: 0;

              &::before {
                  content: "";
                  width: 1px;
                  height: 80px;
                  background: rgb(255 255 255 / 10%);
                  position: absolute;
                  right: 14px;
                  top: -19px;

                  @media #{$lg,$md,$sm,$xs} {
                      display: none;
                  }
              }
          }
      }

      & .main-menu {
          text-align: end;

          ul {
              line-height: 1;

              >li {
                  display: inline-block;
                  line-height: 1;

                  >a {
                      display: inline-block;
                      font-weight: 500;
                      font-size: 15px;
                      color: #fff;
                      text-transform: capitalize;
                      line-height: 1;
                      padding: 31px 18px;
                      overflow: hidden;
                      font-family: $text-font2;

                      @media (max-width: 1191px) {
                          padding: 30px 15px;
                      }

                      &.active {
                          color: #FF5E14;
                      }

                      i {
                          font-size: 11px;
                          transition: .3s;
                      }
                  }

                  &:hover {
                      >a {
                          color: #FF5E14;

                          i {
                              transform: rotate(90deg);
                          }
                      }

                      >ul {
                          opacity: 1;
                          visibility: visible;
                          transform: translateY(0);
                      }
                  }

                  >ul {
                      position: absolute;
                      top: 100%;
                      width: 200px;
                      background-color: $white;
                      left: 0;
                      z-index: 999;
                      box-shadow: 0 13px 35px -12px rgba(35, 35, 35, 0.15);
                      visibility: hidden;
                      opacity: 0;
                      text-align: start;
                      transform: translateY(40px);
                      transition: all .5s ease-in-out;
                      padding: 10px 0px;

                      li {
                          display: block;

                          a {
                              display: block;
                              color: #252525;
                              padding: 12px 25px;
                              font-family: $text-font2;

                              &:hover {
                                  color: #FF5E14;
                              }
                          }
                      }

                      ul {
                          left: 100%;
                          top: 0;
                      }
                  }
              }
          }
      }

      & .header-right {
          &-elements {
              position: relative;

              &::before {
                  content: "";
                  width: 1px;
                  height: 80px;
                  background: rgb(255 255 255 / 10%);
                  position: absolute;
                  left: 12px;
                  top: -26px;

                  @media #{$lg,$md,$sm,$xs} {
                      display: none;
                  }
              }
          }

          &__search {
              &__wrapper {
                  display: flex;
                  align-items: center;
                  gap: 30px;
                  margin-left: -10px;

                  i {
                      font-size: 20px;
                      color: #fff;
                      cursor: pointer;
                      transition: .3s;

                      &:hover {
                          color: #FF5E14;
                      }
                  }

                  a {
                      position: relative;

                      span {
                          width: 14px;
                          height: 14px;
                          background: #FF5E14;
                          display: inline-block;
                          font-size: 10px;
                          text-align: center;
                          line-height: 14px;
                          color: #fff;
                          border-radius: 30px;
                          font-weight: 600;
                          position: absolute;
                          top: 0px;
                          right: -8px;
                      }
                  }
              }
          }
      }

      & #hamburger i {
          color: #fff;
      }
  }