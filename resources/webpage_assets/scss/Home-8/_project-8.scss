.project-8 {
    &__top {
        &__wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;

            @media #{$sm,$xs} {
                display: block;
            }
        }
    }

    &__thumb {
        position: relative;

        @media #{$sm} {
            width: 410px;
            margin: auto;
        }

        @media #{$xs} {
            width: 310px;
            margin: auto;
        }

        img {
            max-width: inherit;
        }

        &::before {
            content: "";
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            position: absolute;
            transition: .3s;
            background: linear-gradient(180deg, rgba(20, 24, 32, 0) 0%, #141820 100%);
        }

        >a {
            width: 44px;
            height: 44px;
            border: 2px solid #fff;
            position: absolute;
            border-radius: 500px;
            text-align: center;
            line-height: 44px;
            color: #fff;
            font-size: 16px;
            font-weight: 100;
            right: 28px;
            top: 0px;
            opacity: 0;
            visibility: hidden;
            transition: .3s;
        }

        &:hover {
            >a {
                opacity: 1;
                visibility: visible;
                top: 30px;
            }

            &.project-8 {
                &__thumb {
                    &::before {
                        background: rgba(20, 24, 32, 0.7);
                    }
                }
            }

            & .project-8 {
                &__dec {
                    &::after {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
        }
    }

    &__content {
        position: absolute;
        left: 30px;
        bottom: 32px;
    }

    &__title {
        font-size: 26px;
        color: #fff;
        font-weight: 700;
        text-transform: none;
        margin-bottom: 0px;
        letter-spacing: -0.02em;
        font-family: $text-font2;

        a {
            &:hover {
                color: #FF5E14;
            }
        }
    }

    &__dec {
        font-size: 16px;
        font-weight: 400;
        color: #fff;
        font-family: $text-font2;

        &::after {
            content: "";
            width: 40px;
            height: 2px;
            background-color: #fff;
            position: absolute;
            bottom: 11px;
            margin-left: 5px;
            opacity: 0;
            visibility: visible;
            transition: .3s;
        }
    }
}