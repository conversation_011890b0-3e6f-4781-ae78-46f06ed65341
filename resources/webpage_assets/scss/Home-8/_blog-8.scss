.blog-8 {
    &__meta {
        ul {
            li {
                font-size: 14px;
                color: #53545A;
                font-weight: 500;
                display: inline-block;
                font-family: $text-font2;
                position: relative;
                padding-right: 12px;
                margin-right: 5px;

                &::before {
                    content: "";
                    width: 4px;
                    height: 4px;
                    background: #53545A;
                    position: absolute;
                    border-radius: 30px;
                    right: 0;
                    top: 13px;
                }

                &:last-of-type {
                    padding-right: 0px;
                    margin-right: 0px;

                    &::before {
                        display: none;
                    }
                }
            }
        }
    }

    &__thumb {
        width: 410px;
        height: 280px;
        margin-bottom: 20px;
        overflow: hidden;

        @media #{$xl} {
            width: 400px;
        }

        @media #{$lg,$md,$xs} {
            width: inherit;
            height: inherit;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: .3s;

            &:hover {
                transform: scale(1.2);
            }
        }
    }

    &__item {
        padding-bottom: 30px;
        border-bottom: 1px solid rgb(20 24 32 / 10%);
        transition: .3s;
        position: relative;

        @media #{$sm} {
            width: 410px;
            margin: auto;
        }

        @media #{$md,$sm,$xs} {

            &.item-1,
            &.item-2 {
                margin-bottom: 30px;
            }
        }

        &::before {
            content: "";
            width: 0;
            height: 1px;
            background: #FF5E14;
            position: absolute;
            bottom: 0;
            transition: .3s;
        }

        &:hover {
            &::before {
                width: 100%;
            }
        }
    }

    &__title {
        font-size: 22px;
        color: #141820;
        font-weight: 700;
        line-height: 30px;
        text-transform: none;
        margin-top: 5px;
        margin-bottom: 24px;
        font-family: $text-font2;

        &:hover {
            color: #FF5E14;
        }

        @media #{$lg} {
            font-size: 21px;
        }
        @media #{$xs} {
            font-size: 20px;
        }
    }

    &__author {
        display: flex;
        align-items: center;
        gap: 10px;

        &__thumb {
            width: 30px;
            height: 30px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 100px;
            }
        }

        &__name {
            font-size: 14px;
            font-weight: 600;
            text-transform: none;
            color: #141820;
            margin: 0;
            font-family: $text-font2;
        }
    }
}

section.brand__area.brand-8 {
    background: #141820;
    padding: 76px 0;
}