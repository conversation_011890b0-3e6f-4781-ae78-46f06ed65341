.fade-custom-anim-right {
  animation: fade-anim-right 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
  opacity: 0;
}

@keyframes fade-anim-right {
  0% {
    transform: translateX(5%);
    clip-path: inset(0 0 0 100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}

.fade-custom-anim-left {
  animation: fade-anim-left 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
  opacity: 0;
}

@keyframes fade-anim-left {
  0% {
    transform: translateX(-5%);
    clip-path: inset(0 100% 0 0);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}

.fade-custom-anim-top {
  animation: fade-anim-top 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
  opacity: 0;
}

@keyframes fade-anim-top {
  0% {
    transform: translateY(-5%);
    clip-path: inset(0 0 100% 0);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}

.rrFadeInUp {
  opacity: 0;
  animation: rrFadeInUp 2s ease-out forwards;
}

@keyframes rrFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.rrfadeInLeft {
  opacity: 0;
  animation: mkfadeInLeft 2s ease-out forwards;
}

@keyframes rrfadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// panel
@keyframes clip-a-z {
  0% {
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
  }

  100% {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

.clip-a-z {
  z-index: 10;
  transition: transform 0.5s ease-in-out;
  animation: clip-a-z 2s;
  animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

@keyframes panel {
  0% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

.panel {
  position: absolute;
  top: 0;
  width: 0%;
  height: 100%;
  left: 0;
  background: var(--rr-theme-primary);
  z-index: 10;
  opacity: .9;
  transition: transform 0.5s ease-in-out;
  animation: panel 2s;
  animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

@keyframes zoomInOut {
  0% {
    transform: scale(1);
    /* Initial size */
  }

  50% {
    transform: scale(1.1);
    /* Zoom in */
  }

  100% {
    transform: scale(1);
    /* Zoom out */
  }
}

.rrupdown {
  animation: rrupdown 3s infinite;
}

@keyframes rrupdown {
  0% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }

  50% {
    -webkit-transform: translateY(-20px);
    -moz-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    -o-transform: translateY(-20px);
    transform: translateY(-20px);
  }

  100% {
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}

.rrLeftRight {
  animation: rrLeftRight 5s infinite;
}

@keyframes rrLeftRight {
  0% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    -webkit-transform: translateX(-70px);
    -moz-transform: translateX(-70px);
    -ms-transform: translateX(-70px);
    -o-transform: translateX(-70px);
    transform: translateX(-70px);
  }

  100% {
    -webkit-transform: translateX(0px);
    -moz-transform: translateX(0px);
    -ms-transform: translateX(0px);
    -o-transform: translateX(0px);
    transform: translateX(0px);
  }
}

// -------------------------end----------------------------------------------------


@keyframes playVideo
{
    0% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, .3);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, .3);
    }
    40% {
        -webkit-box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
                box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }
    80% {
        -webkit-box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
                box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }
    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes dimond {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-webkit-keyframes bounce {
  0%, 100% { -webkit-transform: scale(0.0) }
  50% { -webkit-transform: scale(1.0) }
}

@keyframes bounce {
  0%, 100% { 
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 50% { 
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}
@-webkit-keyframes movebounce {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(20px);
            transform: translateY(20px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}

@keyframes movebounce {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(20px);
            transform: translateY(20px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}
@-webkit-keyframes bounceUp {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(40px);
            transform: translateY(40px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}

@keyframes bounceUp {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(40px);
            transform: translateY(40px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}

@-webkit-keyframes moveleftbounce {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(40px);
            transform: translateX(40px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

@keyframes moveleftbounce {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(40px);
            transform: translateX(40px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

@-webkit-keyframes moverightbounce {
  0% {
    margin-left: 0px;
  }
  50% {
    margin-left: 30px;
  }
  100% {
    margin-left: 0px;
  }
}

@keyframes moverightbounce {
  0% {
    margin-left: 0px;
  }
  50% {
    margin-left: 30px;
  }
  100% {
    margin-left: 0px;
  }
}


.rotateme {
  -webkit-animation-name: rotateme;
          animation-name: rotateme;
  -webkit-animation-duration: 20s;
          animation-duration: 20s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

@keyframes guraguri {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@-webkit-keyframes guraguri {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate3d {
  0% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
  }
  100% {
    -webkit-transform: rotateY(360deg);
    transform: rotateY(360deg);
  }
}

@keyframes rotate3d {
  0% {
    -webkit-transform: rotateY(0deg);
    transform: rotateY(0deg);
  }
  100% {
    -webkit-transform: rotateY(360deg);
    transform: rotateY(360deg);
  }
}


@keyframes move_wave {
    0% {
        transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        transform: translateX(-25%) translateZ(0) scaleY(0.55)
    }
    100% {
        transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}

@-webkit-keyframes move_wave {
    0% {
        transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        transform: translateX(-25%) translateZ(0) scaleY(0.55)
    }
    100% {
        transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}

/* ----------------------------------
  All Aniamtion Styles
 ------------------------------------ */

 @-webkit-keyframes spinner {
    to {
      -webkit-transform: rotateZ(360deg);
      transform: rotateZ(360deg);
    }
  }
  
  @keyframes spinner {
    to {
      transform: rotateZ(360deg);
    }
  }
  
  @-webkit-keyframes letters-loading {
    0%,
    75%,
    100% {
      opacity: 0;
      transform: rotateY(-90deg);
    }
    25%,
    50% {
      opacity: 1;
      transform: rotateY(0deg);
    }
  }
  
  @keyframes letters-loading {
    0%,
    75%,
    100% {
      opacity: 0;
      transform: rotateY(-90deg);
    }
    25%,
    50% {
      opacity: 1;
      transform: rotateY(0deg);
    }
  }
@keyframes top-to-down {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(20px);
            transform: translateY(20px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}
@keyframes left-to-right {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(20px);
            transform: translateX(20px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

@keyframes shake {
  0%, 20%, 60%, 100% {
	-webkit-transform: translateX(0);
	transform: translateX(0);
  }

  40% {
	-webkit-transform: translateX(-5px);
	transform: translateX(-5px);
  }

  80% {
	-webkit-transform: translateX(-3px);
	transform: translateX(-3px);
  }
}

@keyframes shake-item {
  0%, 20%, 60%, 100% {
	-webkit-transform: translateX(0);
	transform: translateX(0);
  }

  40% {
	-webkit-transform: translateX(5px);
	transform: translateX(5px);
  }

  80% {
	-webkit-transform: translateX(3px);
	transform: translateX(3px);
  }
}


@keyframes top {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(20px);
            transform: translateY(20px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}
@keyframes left-to {
  0% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
  50% {
    -webkit-transform: translateX(20px);
            transform: translateX(20px);
  }
  100% {
    -webkit-transform: translateX(0px);
            transform: translateX(0px);
  }
}

// @keyframes zooms {
//   0%{
//     transform: scale(0.5);
//   }
//   50%{
//     transform: scale(1);
//   }
//   100%{
//     transform: scale(0.5);
//   }
// }
@keyframes top-to-down {
  0% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(20px);
            transform: translateY(20px);
  }
  100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
  }
}

@keyframes playVideo1 {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 94, 20, 0.05);;
    box-shadow: 0 0 0 0 rgba(255, 94, 20, 0.05);;
  }
  40% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 94, 20, 0.05);;
    box-shadow: 0 0 0 30px rgba(255, 94, 20, 0.05);;
  }
  80% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 94, 20, 0.05);;
    box-shadow: 0 0 0 30px rgba(255, 94, 20, 0.05);;
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 94, 20, 0.05);;
    box-shadow: 0 0 0 0 rgba(255, 94, 20, 0.05);;
  }
}


/*fadein custom*/
@-webkit-keyframes fadeInUp2 {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp2 {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.fadeInUp2 {
  -webkit-animation-name: fadeInUp2;
  animation-name: fadeInUp2;
}


/*fadeInUp*/
@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

/* fadeInLeft2  */
@-webkit-keyframes fadeInLeft2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInLeft2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInLeft2 {
  -webkit-animation-name: fadeInLeft2;
  animation-name: fadeInLeft2;
}

/* fadeInRight22 */
@-webkit-keyframes fadeInRight2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes fadeInRight2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(20px);
    -ms-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
}

.fadeInRight2 {
  -webkit-animation-name: fadeInRight2;
  animation-name: fadeInRight2;
}

@keyframes clip-a-z {
	0% {
		clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
	}
	100% {
		clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
	}
}

.clip-a-z {
	z-index: 10;
	transition: transform 0.5s ease-in-out;
	animation: clip-a-z 1s;
	animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

@keyframes qode-draw {
	0%,
	100% {
	 -webkit-clip-path:inset(-2px 0);
	 clip-path:inset(-2px 0)
	}
	42% {
	 -webkit-clip-path:inset(-2px 0 -2px 100%);
	 clip-path:inset(-2px 0 -2px 100%)
	}
	43% {
	 -webkit-clip-path:inset(-2px 100% -2px 0);
	 clip-path:inset(-2px 100% -2px 0)
	}
}