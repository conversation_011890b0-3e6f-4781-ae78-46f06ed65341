(() => {
    var n = {
            576: (n, t, r) => {
                var e;

                function i(n) {
                    return i = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(n) {
                        return typeof n
                    } : function(n) {
                        return n && "function" == typeof Symbol && n.constructor === Symbol && n !== Symbol.prototype ? "symbol" : typeof n
                    }, i(n)
                }
                n = r.nmd(n),
                    function() {
                        var u = "object" == ("undefined" == typeof self ? "undefined" : i(self)) && self.self === self && self || "object" == (void 0 === r.g ? "undefined" : i(r.g)) && r.g.global === r.g && r.g || this || {},
                            o = u._,
                            a = Array.prototype,
                            c = Object.prototype,
                            f = "undefined" != typeof Symbol ? Symbol.prototype : null,
                            l = a.push,
                            s = a.slice,
                            p = c.toString,
                            h = c.hasOwnProperty,
                            d = Array.isArray,
                            v = Object.keys,
                            y = Object.create,
                            g = function() {},
                            m = function n(t) {
                                return t instanceof n ? t : this instanceof n ? void(this._wrapped = t) : new n(t)
                            };
                        t.nodeType ? u._ = m : (!n.nodeType && n.exports && (t = n.exports = m), t._ = m), m.VERSION = "1.9.1";
                        var b, w = function(n, t, r) {
                                if (void 0 === t) return n;
                                switch (null == r ? 3 : r) {
                                    case 1:
                                        return function(r) {
                                            return n.call(t, r)
                                        };
                                    case 3:
                                        return function(r, e, i) {
                                            return n.call(t, r, e, i)
                                        };
                                    case 4:
                                        return function(r, e, i, u) {
                                            return n.call(t, r, e, i, u)
                                        }
                                }
                                return function() {
                                    return n.apply(t, arguments)
                                }
                            },
                            j = function(n, t, r) {
                                return m.iteratee !== b ? m.iteratee(n, t) : null == n ? m.identity : m.isFunction(n) ? w(n, t, r) : m.isObject(n) && !m.isArray(n) ? m.matcher(n) : m.property(n)
                            };
                        m.iteratee = b = function(n, t) {
                            return j(n, t, 1 / 0)
                        };
                        var x = function(n, t) {
                                return t = null == t ? n.length - 1 : +t,
                                    function() {
                                        for (var r = Math.max(arguments.length - t, 0), e = Array(r), i = 0; i < r; i++) e[i] = arguments[i + t];
                                        switch (t) {
                                            case 0:
                                                return n.call(this, e);
                                            case 1:
                                                return n.call(this, arguments[0], e);
                                            case 2:
                                                return n.call(this, arguments[0], arguments[1], e)
                                        }
                                        var u = Array(t + 1);
                                        for (i = 0; i < t; i++) u[i] = arguments[i];
                                        return u[t] = e, n.apply(this, u)
                                    }
                            },
                            O = function(n) {
                                if (!m.isObject(n)) return {};
                                if (y) return y(n);
                                g.prototype = n;
                                var t = new g;
                                return g.prototype = null, t
                            },
                            A = function(n) {
                                return function(t) {
                                    return null == t ? void 0 : t[n]
                                }
                            },
                            _ = function(n, t) {
                                return null != n && h.call(n, t)
                            },
                            S = function(n, t) {
                                for (var r = t.length, e = 0; e < r; e++) {
                                    if (null == n) return;
                                    n = n[t[e]]
                                }
                                return r ? n : void 0
                            },
                            k = Math.pow(2, 53) - 1,
                            E = A("length"),
                            F = function(n) {
                                var t = E(n);
                                return "number" == typeof t && t >= 0 && t <= k
                            };
                        m.each = m.forEach = function(n, t, r) {
                            var e, i;
                            if (t = w(t, r), F(n))
                                for (e = 0, i = n.length; e < i; e++) t(n[e], e, n);
                            else {
                                var u = m.keys(n);
                                for (e = 0, i = u.length; e < i; e++) t(n[u[e]], u[e], n)
                            }
                            return n
                        }, m.map = m.collect = function(n, t, r) {
                            t = j(t, r);
                            for (var e = !F(n) && m.keys(n), i = (e || n).length, u = Array(i), o = 0; o < i; o++) {
                                var a = e ? e[o] : o;
                                u[o] = t(n[a], a, n)
                            }
                            return u
                        };
                        var I = function(n) {
                            var t = function(t, r, e, i) {
                                var u = !F(t) && m.keys(t),
                                    o = (u || t).length,
                                    a = n > 0 ? 0 : o - 1;
                                for (i || (e = t[u ? u[a] : a], a += n); a >= 0 && a < o; a += n) {
                                    var c = u ? u[a] : a;
                                    e = r(e, t[c], c, t)
                                }
                                return e
                            };
                            return function(n, r, e, i) {
                                var u = arguments.length >= 3;
                                return t(n, w(r, i, 4), e, u)
                            }
                        };
                        m.reduce = m.foldl = m.inject = I(1), m.reduceRight = m.foldr = I(-1), m.find = m.detect = function(n, t, r) {
                            var e = (F(n) ? m.findIndex : m.findKey)(n, t, r);
                            if (void 0 !== e && -1 !== e) return n[e]
                        }, m.filter = m.select = function(n, t, r) {
                            var e = [];
                            return t = j(t, r), m.each(n, (function(n, r, i) {
                                t(n, r, i) && e.push(n)
                            })), e
                        }, m.reject = function(n, t, r) {
                            return m.filter(n, m.negate(j(t)), r)
                        }, m.every = m.all = function(n, t, r) {
                            t = j(t, r);
                            for (var e = !F(n) && m.keys(n), i = (e || n).length, u = 0; u < i; u++) {
                                var o = e ? e[u] : u;
                                if (!t(n[o], o, n)) return !1
                            }
                            return !0
                        }, m.some = m.any = function(n, t, r) {
                            t = j(t, r);
                            for (var e = !F(n) && m.keys(n), i = (e || n).length, u = 0; u < i; u++) {
                                var o = e ? e[u] : u;
                                if (t(n[o], o, n)) return !0
                            }
                            return !1
                        }, m.contains = m.includes = m.include = function(n, t, r, e) {
                            return F(n) || (n = m.values(n)), ("number" != typeof r || e) && (r = 0), m.indexOf(n, t, r) >= 0
                        }, m.invoke = x((function(n, t, r) {
                            var e, i;
                            return m.isFunction(t) ? i = t : m.isArray(t) && (e = t.slice(0, -1), t = t[t.length - 1]), m.map(n, (function(n) {
                                var u = i;
                                if (!u) {
                                    if (e && e.length && (n = S(n, e)), null == n) return;
                                    u = n[t]
                                }
                                return null == u ? u : u.apply(n, r)
                            }))
                        })), m.pluck = function(n, t) {
                            return m.map(n, m.property(t))
                        }, m.where = function(n, t) {
                            return m.filter(n, m.matcher(t))
                        }, m.findWhere = function(n, t) {
                            return m.find(n, m.matcher(t))
                        }, m.max = function(n, t, r) {
                            var e, u, o = -1 / 0,
                                a = -1 / 0;
                            if (null == t || "number" == typeof t && "object" != i(n[0]) && null != n)
                                for (var c = 0, f = (n = F(n) ? n : m.values(n)).length; c < f; c++) null != (e = n[c]) && e > o && (o = e);
                            else t = j(t, r), m.each(n, (function(n, r, e) {
                                ((u = t(n, r, e)) > a || u === -1 / 0 && o === -1 / 0) && (o = n, a = u)
                            }));
                            return o
                        }, m.min = function(n, t, r) {
                            var e, u, o = 1 / 0,
                                a = 1 / 0;
                            if (null == t || "number" == typeof t && "object" != i(n[0]) && null != n)
                                for (var c = 0, f = (n = F(n) ? n : m.values(n)).length; c < f; c++) null != (e = n[c]) && e < o && (o = e);
                            else t = j(t, r), m.each(n, (function(n, r, e) {
                                ((u = t(n, r, e)) < a || u === 1 / 0 && o === 1 / 0) && (o = n, a = u)
                            }));
                            return o
                        }, m.shuffle = function(n) {
                            return m.sample(n, 1 / 0)
                        }, m.sample = function(n, t, r) {
                            if (null == t || r) return F(n) || (n = m.values(n)), n[m.random(n.length - 1)];
                            var e = F(n) ? m.clone(n) : m.values(n),
                                i = E(e);
                            t = Math.max(Math.min(t, i), 0);
                            for (var u = i - 1, o = 0; o < t; o++) {
                                var a = m.random(o, u),
                                    c = e[o];
                                e[o] = e[a], e[a] = c
                            }
                            return e.slice(0, t)
                        }, m.sortBy = function(n, t, r) {
                            var e = 0;
                            return t = j(t, r), m.pluck(m.map(n, (function(n, r, i) {
                                return {
                                    value: n,
                                    index: e++,
                                    criteria: t(n, r, i)
                                }
                            })).sort((function(n, t) {
                                var r = n.criteria,
                                    e = t.criteria;
                                if (r !== e) {
                                    if (r > e || void 0 === r) return 1;
                                    if (r < e || void 0 === e) return -1
                                }
                                return n.index - t.index
                            })), "value")
                        };
                        var M = function(n, t) {
                            return function(r, e, i) {
                                var u = t ? [
                                    [],
                                    []
                                ] : {};
                                return e = j(e, i), m.each(r, (function(t, i) {
                                    var o = e(t, i, r);
                                    n(u, t, o)
                                })), u
                            }
                        };
                        m.groupBy = M((function(n, t, r) {
                            _(n, r) ? n[r].push(t) : n[r] = [t]
                        })), m.indexBy = M((function(n, t, r) {
                            n[r] = t
                        })), m.countBy = M((function(n, t, r) {
                            _(n, r) ? n[r]++ : n[r] = 1
                        }));
                        var N = /[^\ud800-\udfff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff]/g;
                        m.toArray = function(n) {
                            return n ? m.isArray(n) ? s.call(n) : m.isString(n) ? n.match(N) : F(n) ? m.map(n, m.identity) : m.values(n) : []
                        }, m.size = function(n) {
                            return null == n ? 0 : F(n) ? n.length : m.keys(n).length
                        }, m.partition = M((function(n, t, r) {
                            n[r ? 0 : 1].push(t)
                        }), !0), m.first = m.head = m.take = function(n, t, r) {
                            return null == n || n.length < 1 ? null == t ? void 0 : [] : null == t || r ? n[0] : m.initial(n, n.length - t)
                        }, m.initial = function(n, t, r) {
                            return s.call(n, 0, Math.max(0, n.length - (null == t || r ? 1 : t)))
                        }, m.last = function(n, t, r) {
                            return null == n || n.length < 1 ? null == t ? void 0 : [] : null == t || r ? n[n.length - 1] : m.rest(n, Math.max(0, n.length - t))
                        }, m.rest = m.tail = m.drop = function(n, t, r) {
                            return s.call(n, null == t || r ? 1 : t)
                        }, m.compact = function(n) {
                            return m.filter(n, Boolean)
                        };
                        var P = function n(t, r, e, i) {
                            for (var u = (i = i || []).length, o = 0, a = E(t); o < a; o++) {
                                var c = t[o];
                                if (F(c) && (m.isArray(c) || m.isArguments(c)))
                                    if (r)
                                        for (var f = 0, l = c.length; f < l;) i[u++] = c[f++];
                                    else n(c, r, e, i), u = i.length;
                                else e || (i[u++] = c)
                            }
                            return i
                        };
                        m.flatten = function(n, t) {
                            return P(n, t, !1)
                        }, m.without = x((function(n, t) {
                            return m.difference(n, t)
                        })), m.uniq = m.unique = function(n, t, r, e) {
                            m.isBoolean(t) || (e = r, r = t, t = !1), null != r && (r = j(r, e));
                            for (var i = [], u = [], o = 0, a = E(n); o < a; o++) {
                                var c = n[o],
                                    f = r ? r(c, o, n) : c;
                                t && !r ? (o && u === f || i.push(c), u = f) : r ? m.contains(u, f) || (u.push(f), i.push(c)) : m.contains(i, c) || i.push(c)
                            }
                            return i
                        }, m.union = x((function(n) {
                            return m.uniq(P(n, !0, !0))
                        })), m.intersection = function(n) {
                            for (var t = [], r = arguments.length, e = 0, i = E(n); e < i; e++) {
                                var u = n[e];
                                if (!m.contains(t, u)) {
                                    var o = void 0;
                                    for (o = 1; o < r && m.contains(arguments[o], u); o++);
                                    o === r && t.push(u)
                                }
                            }
                            return t
                        }, m.difference = x((function(n, t) {
                            return t = P(t, !0, !0), m.filter(n, (function(n) {
                                return !m.contains(t, n)
                            }))
                        })), m.unzip = function(n) {
                            for (var t = n && m.max(n, E).length || 0, r = Array(t), e = 0; e < t; e++) r[e] = m.pluck(n, e);
                            return r
                        }, m.zip = x(m.unzip), m.object = function(n, t) {
                            for (var r = {}, e = 0, i = E(n); e < i; e++) t ? r[n[e]] = t[e] : r[n[e][0]] = n[e][1];
                            return r
                        };
                        var R = function(n) {
                            return function(t, r, e) {
                                r = j(r, e);
                                for (var i = E(t), u = n > 0 ? 0 : i - 1; u >= 0 && u < i; u += n)
                                    if (r(t[u], u, t)) return u;
                                return -1
                            }
                        };
                        m.findIndex = R(1), m.findLastIndex = R(-1), m.sortedIndex = function(n, t, r, e) {
                            for (var i = (r = j(r, e, 1))(t), u = 0, o = E(n); u < o;) {
                                var a = Math.floor((u + o) / 2);
                                r(n[a]) < i ? u = a + 1 : o = a
                            }
                            return u
                        };
                        var T = function(n, t, r) {
                            return function(e, i, u) {
                                var o = 0,
                                    a = E(e);
                                if ("number" == typeof u) n > 0 ? o = u >= 0 ? u : Math.max(u + a, o) : a = u >= 0 ? Math.min(u + 1, a) : u + a + 1;
                                else if (r && u && a) return e[u = r(e, i)] === i ? u : -1;
                                if (i != i) return (u = t(s.call(e, o, a), m.isNaN)) >= 0 ? u + o : -1;
                                for (u = n > 0 ? o : a - 1; u >= 0 && u < a; u += n)
                                    if (e[u] === i) return u;
                                return -1
                            }
                        };
                        m.indexOf = T(1, m.findIndex, m.sortedIndex), m.lastIndexOf = T(-1, m.findLastIndex), m.range = function(n, t, r) {
                            null == t && (t = n || 0, n = 0), r || (r = t < n ? -1 : 1);
                            for (var e = Math.max(Math.ceil((t - n) / r), 0), i = Array(e), u = 0; u < e; u++, n += r) i[u] = n;
                            return i
                        }, m.chunk = function(n, t) {
                            if (null == t || t < 1) return [];
                            for (var r = [], e = 0, i = n.length; e < i;) r.push(s.call(n, e, e += t));
                            return r
                        };
                        var z = function(n, t, r, e, i) {
                            if (!(e instanceof t)) return n.apply(r, i);
                            var u = O(n.prototype),
                                o = n.apply(u, i);
                            return m.isObject(o) ? o : u
                        };
                        m.bind = x((function(n, t, r) {
                            if (!m.isFunction(n)) throw new TypeError("Bind must be called on a function");
                            var e = x((function(i) {
                                return z(n, e, t, this, r.concat(i))
                            }));
                            return e
                        })), m.partial = x((function(n, t) {
                            var r = m.partial.placeholder;
                            return function e() {
                                for (var i = 0, u = t.length, o = Array(u), a = 0; a < u; a++) o[a] = t[a] === r ? arguments[i++] : t[a];
                                for (; i < arguments.length;) o.push(arguments[i++]);
                                return z(n, e, this, this, o)
                            }
                        })), m.partial.placeholder = m, m.bindAll = x((function(n, t) {
                            var r = (t = P(t, !1, !1)).length;
                            if (r < 1) throw new Error("bindAll must be passed function names");
                            for (; r--;) {
                                var e = t[r];
                                n[e] = m.bind(n[e], n)
                            }
                        })), m.memoize = function(n, t) {
                            var r = function r(e) {
                                var i = r.cache,
                                    u = "" + (t ? t.apply(this, arguments) : e);
                                return _(i, u) || (i[u] = n.apply(this, arguments)), i[u]
                            };
                            return r.cache = {}, r
                        }, m.delay = x((function(n, t, r) {
                            return setTimeout((function() {
                                return n.apply(null, r)
                            }), t)
                        })), m.defer = m.partial(m.delay, m, 1), m.throttle = function(n, t, r) {
                            var e, i, u, o, a = 0;
                            r || (r = {});
                            var c = function() {
                                    a = !1 === r.leading ? 0 : m.now(), e = null, o = n.apply(i, u), e || (i = u = null)
                                },
                                f = function() {
                                    var f = m.now();
                                    a || !1 !== r.leading || (a = f);
                                    var l = t - (f - a);
                                    return i = this, u = arguments, l <= 0 || l > t ? (e && (clearTimeout(e), e = null), a = f, o = n.apply(i, u), e || (i = u = null)) : e || !1 === r.trailing || (e = setTimeout(c, l)), o
                                };
                            return f.cancel = function() {
                                clearTimeout(e), a = 0, e = i = u = null
                            }, f
                        }, m.debounce = function(n, t, r) {
                            var e, i, u = function(t, r) {
                                    e = null, r && (i = n.apply(t, r))
                                },
                                o = x((function(o) {
                                    if (e && clearTimeout(e), r) {
                                        var a = !e;
                                        e = setTimeout(u, t), a && (i = n.apply(this, o))
                                    } else e = m.delay(u, t, this, o);
                                    return i
                                }));
                            return o.cancel = function() {
                                clearTimeout(e), e = null
                            }, o
                        }, m.wrap = function(n, t) {
                            return m.partial(t, n)
                        }, m.negate = function(n) {
                            return function() {
                                return !n.apply(this, arguments)
                            }
                        }, m.compose = function() {
                            var n = arguments,
                                t = n.length - 1;
                            return function() {
                                for (var r = t, e = n[t].apply(this, arguments); r--;) e = n[r].call(this, e);
                                return e
                            }
                        }, m.after = function(n, t) {
                            return function() {
                                if (--n < 1) return t.apply(this, arguments)
                            }
                        }, m.before = function(n, t) {
                            var r;
                            return function() {
                                return --n > 0 && (r = t.apply(this, arguments)), n <= 1 && (t = null), r
                            }
                        }, m.once = m.partial(m.before, 2), m.restArguments = x;
                        var B = !{
                                toString: null
                            }.propertyIsEnumerable("toString"),
                            q = ["valueOf", "isPrototypeOf", "toString", "propertyIsEnumerable", "hasOwnProperty", "toLocaleString"],
                            D = function(n, t) {
                                var r = q.length,
                                    e = n.constructor,
                                    i = m.isFunction(e) && e.prototype || c,
                                    u = "constructor";
                                for (_(n, u) && !m.contains(t, u) && t.push(u); r--;)(u = q[r]) in n && n[u] !== i[u] && !m.contains(t, u) && t.push(u)
                            };
                        m.keys = function(n) {
                            if (!m.isObject(n)) return [];
                            if (v) return v(n);
                            var t = [];
                            for (var r in n) _(n, r) && t.push(r);
                            return B && D(n, t), t
                        }, m.allKeys = function(n) {
                            if (!m.isObject(n)) return [];
                            var t = [];
                            for (var r in n) t.push(r);
                            return B && D(n, t), t
                        }, m.values = function(n) {
                            for (var t = m.keys(n), r = t.length, e = Array(r), i = 0; i < r; i++) e[i] = n[t[i]];
                            return e
                        }, m.mapObject = function(n, t, r) {
                            t = j(t, r);
                            for (var e = m.keys(n), i = e.length, u = {}, o = 0; o < i; o++) {
                                var a = e[o];
                                u[a] = t(n[a], a, n)
                            }
                            return u
                        }, m.pairs = function(n) {
                            for (var t = m.keys(n), r = t.length, e = Array(r), i = 0; i < r; i++) e[i] = [t[i], n[t[i]]];
                            return e
                        }, m.invert = function(n) {
                            for (var t = {}, r = m.keys(n), e = 0, i = r.length; e < i; e++) t[n[r[e]]] = r[e];
                            return t
                        }, m.functions = m.methods = function(n) {
                            var t = [];
                            for (var r in n) m.isFunction(n[r]) && t.push(r);
                            return t.sort()
                        };
                        var W = function(n, t) {
                            return function(r) {
                                var e = arguments.length;
                                if (t && (r = Object(r)), e < 2 || null == r) return r;
                                for (var i = 1; i < e; i++)
                                    for (var u = arguments[i], o = n(u), a = o.length, c = 0; c < a; c++) {
                                        var f = o[c];
                                        t && void 0 !== r[f] || (r[f] = u[f])
                                    }
                                return r
                            }
                        };
                        m.extend = W(m.allKeys), m.extendOwn = m.assign = W(m.keys), m.findKey = function(n, t, r) {
                            t = j(t, r);
                            for (var e, i = m.keys(n), u = 0, o = i.length; u < o; u++)
                                if (t(n[e = i[u]], e, n)) return e
                        };
                        var H, K, C = function(n, t, r) {
                            return t in r
                        };
                        m.pick = x((function(n, t) {
                            var r = {},
                                e = t[0];
                            if (null == n) return r;
                            m.isFunction(e) ? (t.length > 1 && (e = w(e, t[1])), t = m.allKeys(n)) : (e = C, t = P(t, !1, !1), n = Object(n));
                            for (var i = 0, u = t.length; i < u; i++) {
                                var o = t[i],
                                    a = n[o];
                                e(a, o, n) && (r[o] = a)
                            }
                            return r
                        })), m.omit = x((function(n, t) {
                            var r, e = t[0];
                            return m.isFunction(e) ? (e = m.negate(e), t.length > 1 && (r = t[1])) : (t = m.map(P(t, !1, !1), String), e = function(n, r) {
                                return !m.contains(t, r)
                            }), m.pick(n, e, r)
                        })), m.defaults = W(m.allKeys, !0), m.create = function(n, t) {
                            var r = O(n);
                            return t && m.extendOwn(r, t), r
                        }, m.clone = function(n) {
                            return m.isObject(n) ? m.isArray(n) ? n.slice() : m.extend({}, n) : n
                        }, m.tap = function(n, t) {
                            return t(n), n
                        }, m.isMatch = function(n, t) {
                            var r = m.keys(t),
                                e = r.length;
                            if (null == n) return !e;
                            for (var i = Object(n), u = 0; u < e; u++) {
                                var o = r[u];
                                if (t[o] !== i[o] || !(o in i)) return !1
                            }
                            return !0
                        }, H = function(n, t, r, e) {
                            if (n === t) return 0 !== n || 1 / n == 1 / t;
                            if (null == n || null == t) return !1;
                            if (n != n) return t != t;
                            var u = i(n);
                            return ("function" === u || "object" === u || "object" == i(t)) && K(n, t, r, e)
                        }, K = function(n, t, r, e) {
                            n instanceof m && (n = n._wrapped), t instanceof m && (t = t._wrapped);
                            var u = p.call(n);
                            if (u !== p.call(t)) return !1;
                            switch (u) {
                                case "[object RegExp]":
                                case "[object String]":
                                    return "" + n == "" + t;
                                case "[object Number]":
                                    return +n != +n ? +t != +t : 0 == +n ? 1 / +n == 1 / t : +n == +t;
                                case "[object Date]":
                                case "[object Boolean]":
                                    return +n == +t;
                                case "[object Symbol]":
                                    return f.valueOf.call(n) === f.valueOf.call(t)
                            }
                            var o = "[object Array]" === u;
                            if (!o) {
                                if ("object" != i(n) || "object" != i(t)) return !1;
                                var a = n.constructor,
                                    c = t.constructor;
                                if (a !== c && !(m.isFunction(a) && a instanceof a && m.isFunction(c) && c instanceof c) && "constructor" in n && "constructor" in t) return !1
                            }
                            e = e || [];
                            for (var l = (r = r || []).length; l--;)
                                if (r[l] === n) return e[l] === t;
                            if (r.push(n), e.push(t), o) {
                                if ((l = n.length) !== t.length) return !1;
                                for (; l--;)
                                    if (!H(n[l], t[l], r, e)) return !1
                            } else {
                                var s, h = m.keys(n);
                                if (l = h.length, m.keys(t).length !== l) return !1;
                                for (; l--;)
                                    if (s = h[l], !_(t, s) || !H(n[s], t[s], r, e)) return !1
                            }
                            return r.pop(), e.pop(), !0
                        }, m.isEqual = function(n, t) {
                            return H(n, t)
                        }, m.isEmpty = function(n) {
                            return null == n || (F(n) && (m.isArray(n) || m.isString(n) || m.isArguments(n)) ? 0 === n.length : 0 === m.keys(n).length)
                        }, m.isElement = function(n) {
                            return !(!n || 1 !== n.nodeType)
                        }, m.isArray = d || function(n) {
                            return "[object Array]" === p.call(n)
                        }, m.isObject = function(n) {
                            var t = i(n);
                            return "function" === t || "object" === t && !!n
                        }, m.each(["Arguments", "Function", "String", "Number", "Date", "RegExp", "Error", "Symbol", "Map", "WeakMap", "Set", "WeakSet"], (function(n) {
                            m["is" + n] = function(t) {
                                return p.call(t) === "[object " + n + "]"
                            }
                        })), m.isArguments(arguments) || (m.isArguments = function(n) {
                            return _(n, "callee")
                        });
                        var L = u.document && u.document.childNodes;
                        "object" != ("undefined" == typeof Int8Array ? "undefined" : i(Int8Array)) && "function" != typeof L && (m.isFunction = function(n) {
                            return "function" == typeof n || !1
                        }), m.isFinite = function(n) {
                            return !m.isSymbol(n) && isFinite(n) && !isNaN(parseFloat(n))
                        }, m.isNaN = function(n) {
                            return m.isNumber(n) && isNaN(n)
                        }, m.isBoolean = function(n) {
                            return !0 === n || !1 === n || "[object Boolean]" === p.call(n)
                        }, m.isNull = function(n) {
                            return null === n
                        }, m.isUndefined = function(n) {
                            return void 0 === n
                        }, m.has = function(n, t) {
                            if (!m.isArray(t)) return _(n, t);
                            for (var r = t.length, e = 0; e < r; e++) {
                                var i = t[e];
                                if (null == n || !h.call(n, i)) return !1;
                                n = n[i]
                            }
                            return !!r
                        }, m.noConflict = function() {
                            return u._ = o, this
                        }, m.identity = function(n) {
                            return n
                        }, m.constant = function(n) {
                            return function() {
                                return n
                            }
                        }, m.noop = function() {}, m.property = function(n) {
                            return m.isArray(n) ? function(t) {
                                return S(t, n)
                            } : A(n)
                        }, m.propertyOf = function(n) {
                            return null == n ? function() {} : function(t) {
                                return m.isArray(t) ? S(n, t) : n[t]
                            }
                        }, m.matcher = m.matches = function(n) {
                            return n = m.extendOwn({}, n),
                                function(t) {
                                    return m.isMatch(t, n)
                                }
                        }, m.times = function(n, t, r) {
                            var e = Array(Math.max(0, n));
                            t = w(t, r, 1);
                            for (var i = 0; i < n; i++) e[i] = t(i);
                            return e
                        }, m.random = function(n, t) {
                            return null == t && (t = n, n = 0), n + Math.floor(Math.random() * (t - n + 1))
                        }, m.now = Date.now || function() {
                            return (new Date).getTime()
                        };
                        var U = {
                                "&": "&amp;",
                                "<": "&lt;",
                                ">": "&gt;",
                                '"': "&quot;",
                                "'": "&#x27;",
                                "`": "&#x60;"
                            },
                            $ = m.invert(U),
                            J = function(n) {
                                var t = function(t) {
                                        return n[t]
                                    },
                                    r = "(?:" + m.keys(n).join("|") + ")",
                                    e = RegExp(r),
                                    i = RegExp(r, "g");
                                return function(n) {
                                    return n = null == n ? "" : "" + n, e.test(n) ? n.replace(i, t) : n
                                }
                            };
                        m.escape = J(U), m.unescape = J($), m.result = function(n, t, r) {
                            m.isArray(t) || (t = [t]);
                            var e = t.length;
                            if (!e) return m.isFunction(r) ? r.call(n) : r;
                            for (var i = 0; i < e; i++) {
                                var u = null == n ? void 0 : n[t[i]];
                                void 0 === u && (u = r, i = e), n = m.isFunction(u) ? u.call(n) : u
                            }
                            return n
                        };
                        var V = 0;
                        m.uniqueId = function(n) {
                            var t = ++V + "";
                            return n ? n + t : t
                        }, m.templateSettings = {
                            evaluate: /<%([\s\S]+?)%>/g,
                            interpolate: /<%=([\s\S]+?)%>/g,
                            escape: /<%-([\s\S]+?)%>/g
                        };
                        var G = /(.)^/,
                            Q = {
                                "'": "'",
                                "\\": "\\",
                                "\r": "r",
                                "\n": "n",
                                "\u2028": "u2028",
                                "\u2029": "u2029"
                            },
                            X = /\\|'|\r|\n|\u2028|\u2029/g,
                            Y = function(n) {
                                return "\\" + Q[n]
                            };
                        m.template = function(n, t, r) {
                            !t && r && (t = r), t = m.defaults({}, t, m.templateSettings);
                            var e, i = RegExp([(t.escape || G).source, (t.interpolate || G).source, (t.evaluate || G).source].join("|") + "|$", "g"),
                                u = 0,
                                o = "__p+='";
                            n.replace(i, (function(t, r, e, i, a) {
                                return o += n.slice(u, a).replace(X, Y), u = a + t.length, r ? o += "'+\n((__t=(" + r + "))==null?'':_.escape(__t))+\n'" : e ? o += "'+\n((__t=(" + e + "))==null?'':__t)+\n'" : i && (o += "';\n" + i + "\n__p+='"), t
                            })), o += "';\n", t.variable || (o = "with(obj||{}){\n" + o + "}\n"), o = "var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n" + o + "return __p;\n";
                            try {
                                e = new Function(t.variable || "obj", "_", o)
                            } catch (n) {
                                throw n.source = o, n
                            }
                            var a = function(n) {
                                    return e.call(this, n, m)
                                },
                                c = t.variable || "obj";
                            return a.source = "function(" + c + "){\n" + o + "}", a
                        }, m.chain = function(n) {
                            var t = m(n);
                            return t._chain = !0, t
                        };
                        var Z = function(n, t) {
                            return n._chain ? m(t).chain() : t
                        };
                        m.mixin = function(n) {
                            return m.each(m.functions(n), (function(t) {
                                var r = m[t] = n[t];
                                m.prototype[t] = function() {
                                    var n = [this._wrapped];
                                    return l.apply(n, arguments), Z(this, r.apply(m, n))
                                }
                            })), m
                        }, m.mixin(m), m.each(["pop", "push", "reverse", "shift", "sort", "splice", "unshift"], (function(n) {
                            var t = a[n];
                            m.prototype[n] = function() {
                                var r = this._wrapped;
                                return t.apply(r, arguments), "shift" !== n && "splice" !== n || 0 !== r.length || delete r[0], Z(this, r)
                            }
                        })), m.each(["concat", "join", "slice"], (function(n) {
                            var t = a[n];
                            m.prototype[n] = function() {
                                return Z(this, t.apply(this._wrapped, arguments))
                            }
                        })), m.prototype.value = function() {
                            return this._wrapped
                        }, m.prototype.valueOf = m.prototype.toJSON = m.prototype.value, m.prototype.toString = function() {
                            return String(this._wrapped)
                        }, void 0 === (e = function() {
                            return m
                        }.apply(t, [])) || (n.exports = e)
                    }()
            }
        },
        t = {};

    function r(e) {
        var i = t[e];
        if (void 0 !== i) return i.exports;
        var u = t[e] = {
            id: e,
            loaded: !1,
            exports: {}
        };
        return n[e](u, u.exports, r), u.loaded = !0, u.exports
    }
    r.g = function() {
        if ("object" == typeof globalThis) return globalThis;
        try {
            return this || new Function("return this")()
        } catch (n) {
            if ("object" == typeof window) return window
        }
    }(), r.nmd = n => (n.paths = [], n.children || (n.children = []), n), (() => {
        function n(n) {
            return function(n) {
                if (Array.isArray(n)) return t(n)
            }(n) || function(n) {
                if ("undefined" != typeof Symbol && null != n[Symbol.iterator] || null != n["@@iterator"]) return Array.from(n)
            }(n) || function(n, r) {
                if (!n) return;
                if ("string" == typeof n) return t(n, r);
                var e = Object.prototype.toString.call(n).slice(8, -1);
                "Object" === e && n.constructor && (e = n.constructor.name);
                if ("Map" === e || "Set" === e) return Array.from(n);
                if ("Arguments" === e || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)) return t(n, r)
            }(n) || function() {
                throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
            }()
        }

        function t(n, t) {
            (null == t || t > n.length) && (t = n.length);
            for (var r = 0, e = new Array(t); r < t; r++) e[r] = n[r];
            return e
        }

        function e(n, t) {
            var r = Object.keys(n);
            if (Object.getOwnPropertySymbols) {
                var e = Object.getOwnPropertySymbols(n);
                t && (e = e.filter((function(t) {
                    return Object.getOwnPropertyDescriptor(n, t).enumerable
                }))), r.push.apply(r, e)
            }
            return r
        }

        function i(n) {
            for (var t = 1; t < arguments.length; t++) {
                var r = null != arguments[t] ? arguments[t] : {};
                t % 2 ? e(Object(r), !0).forEach((function(t) {
                    u(n, t, r[t])
                })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(n, Object.getOwnPropertyDescriptors(r)) : e(Object(r)).forEach((function(t) {
                    Object.defineProperty(n, t, Object.getOwnPropertyDescriptor(r, t))
                }))
            }
            return n
        }

        function u(n, t, r) {
            return t in n ? Object.defineProperty(n, t, {
                value: r,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : n[t] = r, n
        }
        var o = r(576),
            a = {
                color: "orange",
                radius: [.6, 5],
                speed: [1, 3],
                wind: [-1.5, 4]
            },
            c = function(t) {
                var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null,
                    e = arguments.length > 2 ? arguments[2] : void 0,
                    u = i(i({}, a), e),
                    c = u.radius,
                    f = u.speed,
                    l = u.wind,
                    s = u.color,
                    p = {
                        color: s,
                        x: o.random(0, t.offsetWidth),
                        y: o.random(-t.offsetHeight, t.offsetHeight),
                        radius: o.random.apply(o, n(c)),
                        speed: o.random.apply(o, n(f)),
                        wind: o.random.apply(o, n(l)),
                        isResized: !1
                    },
                    h = t.getContext("2d"),
                    d = function() {
                        p.x = o.random(0, t.offsetWidth), p.y = o.random(-t.offsetHeight, 0)
                    },
                    v = function() {
                        return p.isResized = !0
                    },
                    y = function() {
                        h.beginPath(), h.arc(p.x, p.y, p.radius, 0, 2 * Math.PI), h.fillStyle = p.color, h.fill(), h.closePath()
                    },
                    g = r ? function() {
                        return r(h, p)
                    } : y,
                    m = function() {
                        p.y += p.speed, p.x += p.wind
                    },
                    b = function() {
                        p.y < t.offsetHeight || (p.isResized ? (d(), p.isResized = !1) : (p.y = 0, p.x = o.random(0, t.offsetWidth)))
                    },
                    w = function() {
                        m(), b()
                    };
                return {
                    update: w,
                    resized: v,
                    draw: g
                }
            },
            f = (document.getElementById("background_div"), document.querySelector("body"), document.getElementById("bg-canvas")),
            l = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || window.msRequestAnimationFrame;
        ! function(n, t, r) {
            var e = n.getContext("2d"),
                i = [],
                u = function(t) {
                    return i.push(t(n))
                },
                a = function() {
                    e.canvas.width = n.offsetWidth, e.canvas.height = n.offsetHeight, o.forEach(i, (function(n) {
                        return n.resized()
                    }))
                },
                f = function t() {
                    e.clearRect(0, 0, n.offsetWidth, n.offsetHeight), o.forEach(i, (function(n) {
                        return n.draw()
                    })), o.forEach(i, (function(n) {
                        return n.update()
                    })), l(t)
                };
            o.times(t, (function() {
                return u((function(n) {
                    return c(n, null, r)
                }))
            })), window.addEventListener("resize", a), f(), a()
        }(f, 500, {
            color: "white"
        })
    })()
})();
